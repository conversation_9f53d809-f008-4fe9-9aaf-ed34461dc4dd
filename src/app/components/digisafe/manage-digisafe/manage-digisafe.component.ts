import { CommonModule, DatePipe } from '@angular/common'; // Keep DatePipe
import { Component, OnInit, ViewChild, ChangeDetectorRef, <PERSON><PERSON><PERSON>roy, ElementRef, inject, AfterViewInit } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { TabComponent } from '../../../shared/tab/tab.component';
import { PaginationComponent } from '../../../shared/pagination/pagination.component';
import { OffcanvasComponent } from '../../../shared/offcanvas/offcanvas.component';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { PlantManagementService } from '../../../services/plant-management/plant-management.service';
import { DigisafeService } from '../../../services/digisafe/digisafe.service';
import { UpdateService } from '../../../services/update/update.service';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import * as XLSX from 'xlsx';
import { Modal } from 'bootstrap';

import { ClusterService } from '../../../services/master-management/cluster/cluster.service';
import { OpcoService } from '../../../services/master-management/opco/opco.service';
import { PlantTypeService } from '../../../services/master-management/plant-type/plant-type.service';
import { NgSelectModule } from '@ng-select/ng-select';


const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

enum DigisafeStatus {
  Pending = 1,
  Approved = 2,
  Rejected = 3,
}

interface Plant {
    id: number;
    name: string;
    clusterId?: number;
    opcoId?: number;
    plantTypeId?: number;
}

interface AdminUser {
    id: number;
    firstName: string;
    lastName: string;
    contactNumber?: string | null;
    email?: string | null;
}

interface DigisafeFilter {
  plantIds?: number[] | null;
  sort: string;
  enabled?: boolean | null;
  id?: number[] | null;
  fromDate?: string | null;
  toDate?: string | null;
  clusterId?: number | null;
  opcoId?: number | null;
  plantTypeId?: number | null;
}

// --- Interface remains largely the same, just noting which fields become calculated ---
interface DigisafeReport {
    id: number;
    plantId?: number;
    plant?: Plant | null;
    enabled?: boolean;
    isDeleted?: boolean;
    adminId?: number | null;
    approveId?: number | null;
    rejectedId?: number | null;
    updatedId?: number | null;
    status: DigisafeStatus;

    // Core Stats
    AfpCurrent: number | string | null;
    AfpBest: number | string | null;
    ManHoursWorked: number | string | null; // Calculated
    ManHoursWorkedOwnEmployees?: number | string | null;
    ManHoursWorkedConEmployees?: number | string | null;
    KmCovered: number | string | null;

    // Incidents
    OI: number | string | null;
    FAC: number | string | null; // Calculated
    FACCompOnsite?: number | string | null;
    FACCompOfsite?: number | string | null;
    FACconOnsite?: number | string | null;
    FACconOfsite?: number | string | null;
    MTC: number | string | null; // Calculated
    MTCCompOnsite?: number | string | null;
    MTCCompOfsite?: number | string | null;
    MTCconOnsite?: number | string | null;
    MTCconOfsite?: number | string | null;
    MTCthirdPartyVisitor?: number | string | null;
    RLTI: number | string | null; // Calculated
    RLTICompOnsite?: number | string | null;
    RLTICompOfsite?: number | string | null;
    RLTIconOnsite?: number | string | null;
    RLTIconOfsite?: number | string | null;
    RLTIthirdPartyVisitor?: number | string | null;
    Fatal: number | string | null; // Calculated
    FatalCompOnsite?: number | string | null;
    FatalCompOfsite?: number | string | null;
    FatalconOnsite?: number | string | null;
    FatalconOfsite?: number | string | null;
    FatalthirdPartyVisitor?: number | string | null;
    DangerOccurence: number | string | null;
    propertyDamage?: number | string | null;
    FireIncident: number | string | null;
    nonFireIncident?: number | string | null;
    VRA: number | string | null;
    PDI: number | string | null;
    LSI: number | string | null;
    NearMissIncident: number | string | null;

    // Costs & Losses
    AccidentCost: number | string | null;
    MDL: number | string | null; // Calculated
    MDLEmployees?: number | string | null;
    MDLCon?: number | string | null;

    // Safety Activities
    SafetyInduction: number | string | null;
    TBTBatches: number | string | null;
    TBTno: number | string | null;
    STP_Plan: number | string | null;
    STP_MTD: number | string | null;
    ET_Plan: number | string | null;
    ET_MTD: number | string | null;
    TrainingManHours: number | string | null; // Calculated
    TrainingManHoursEmployees?: number | string | null;
    TrainingManHoursCon?: number | string | null;
    SCMPlan: number | string | null;
    SCM_MTD: number | string | null;
    PWAPlan: number | string | null;
    PWA_MTD: number | string | null;
    SA_Plan: number | string | null;
    SA_MTD: number | string | null;
    SI_Plan: number | string | null;
    SI_MTD: number | string | null;
    EI_Plan: number | string | null;
    EI_MTD: number | string | null;
    SPC_Plan: number | string | null;
    SPC_MTD: number | string | null;
    EMD_Plan: number | string | null;
    EMD_MTD: number | string | null;
    IHM_Plan: number | string | null;
    IHM_MTD: number | string | null;
    SafetyWalkthrough: number | string | null;

    // Safety Notifications
    SnRaised: number | string | null;
    SnClosed: number | string | null;
    SnOpen: number | string | null;
    SnOpen90days: number | string | null;

    // RWC / MWD
    RWC?: number | string | null; // Calculated
    RWCCompOnsite?: number | string | null;
    RWCCompOfsite?: number | string | null;
    RWCconOnsite?: number | string | null;
    RWCconOfsite?: number | string | null;
    MWD?: number | string | null; // Calculated
    MWDEmployee?: number | string | null;
    MWDContractorOnsite?: number | string | null;

    // Timestamps & User Info (Unchanged)
    monthAndYear?: string | null;
    createdBy?: number | null;
    createdTimestamp: string | Date;
    updatedTimestamp?: string | Date | null;
    updatedBy?: number | null;
    approvedBy?: number | null;
    rejectedBy?: number | null;
    approvedTimestamp?: string | Date | null;
    rejectedTimestamp?: string | Date | null;
    updated?: AdminUser | null;
    approve?: AdminUser | null;
    rejected?: AdminUser | null;
    rejectionReason?: string | null;
}

// --- MODIFIED Form Data Interface ---
interface DigisafeFormData {
    plantId: number | null;
    // ADDED: Month/Year Selection
    selectedYear: number | null;
    selectedMonth: number | null; // 1-12

    // Fields matching DigisafeReport, values will be bound to form
    // Updated to allow string values for all fields to match pattern validation
    AfpCurrent: number | string | null;
    AfpBest: number | string | null;
    ManHoursWorked: number | string | null; // Readonly display
    ManHoursWorkedOwnEmployees?: number | string | null;
    ManHoursWorkedConEmployees?: number | string | null;
    KmCovered: number | string | null;

    OI: number | string | null;
    FAC: number | string | null; // Readonly display
    FACCompOnsite?: number | string | null;
    FACCompOfsite?: number | string | null;
    FACconOnsite?: number | string | null;
    FACconOfsite?: number | string | null;
    MTC: number | string | null; // Readonly display
    MTCCompOnsite?: number | string | null;
    MTCCompOfsite?: number | string | null;
    MTCconOnsite?: number | string | null;
    MTCconOfsite?: number | string | null;
    MTCthirdPartyVisitor?: number | string | null;
    RLTI: number | string | null; // Readonly display
    RLTICompOnsite?: number | string | null;
    RLTICompOfsite?: number | string | null;
    RLTIconOnsite?: number | string | null;
    RLTIconOfsite?: number | string | null;
    RLTIthirdPartyVisitor?: number | string | null;
    Fatal: number | string | null; // Readonly display
    FatalCompOnsite?: number | string | null;
    FatalCompOfsite?: number | string | null;
    FatalconOnsite?: number | string | null;
    FatalconOfsite?: number | string | null;
    FatalthirdPartyVisitor?: number | string | null;
    DangerOccurence: number | string | null;
    propertyDamage?: number | string | null;
    FireIncident: number | string | null;
    nonFireIncident?: number | string | null;
    VRA: number | string | null;
    PDI: number | string | null;
    LSI: number | string | null;
    NearMissIncident: number | string | null;

    AccidentCost: number | string | null;
    MDL: number | string | null; // Readonly display
    MDLEmployees?: number | string | null;
    MDLCon?: number | string | null;

    SafetyInduction: number | string | null;
    TBTBatches: number | string | null;
    TBTno: number | string | null;
    STP_Plan: number | string | null;
    STP_MTD: number | string | null;
    ET_Plan: number | string | null;
    ET_MTD: number | string | null;
    TrainingManHours: number | string | null; // Readonly display
    TrainingManHoursEmployees?: number | string | null;
    TrainingManHoursCon?: number | string | null;
    SCMPlan: number | string | null;
    SCM_MTD: number | string | null;
    PWAPlan: number | string | null;
    PWA_MTD: number | string | null;
    SA_Plan: number | string | null;
    SA_MTD: number | string | null;
    SI_Plan: number | string | null;
    SI_MTD: number | string | null;
    EI_Plan: number | string | null;
    EI_MTD: number | string | null;
    SPC_Plan: number | string | null;
    SPC_MTD: number | string | null;
    EMD_Plan: number | string | null;
    EMD_MTD: number | string | null;
    IHM_Plan: number | string | null;
    IHM_MTD: number | string | null;
    SafetyWalkthrough: number | string | null;

    SnRaised: number | string | null;
    SnClosed: number | string | null;
    SnOpen: number | string | null;
    SnOpen90days: number | string | null;

    RWC?: number | string | null; // Readonly display
    RWCCompOnsite?: number | string | null;
    RWCCompOfsite?: number | string | null;
    RWCconOnsite?: number | string | null;
    RWCconOfsite?: number | string | null;
    MWD?: number | string | null; // Readonly display
    MWDEmployee?: number | string | null;
    MWDContractorOnsite?: number | string | null;
}

// ADDED: Interface for month/year dropdown options
interface MonthYearOption {
    month: number; // 1-12
    year: number;
    label: string; // e.g., "Mar 2024"
}


@Component({
  selector: 'app-manage-digisafe',
  standalone: true,
  imports: [ /* Keep original imports */
    CommonModule,
    FormsModule,
    TabComponent,
    PaginationComponent,
    OffcanvasComponent,
    NgbDropdownModule,
    ToastMessageComponent,
    NgSelectModule
  ],
  templateUrl: './manage-digisafe.component.html',
  styleUrls: ['./manage-digisafe.component.scss'],
  providers: [DatePipe] // Keep DatePipe
})
export class ManageDigisafeComponent implements OnInit, AfterViewInit, OnDestroy {
    public componentRoles = ROLES;

    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild('editForm') editForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
    @ViewChild('digisafeActionConfirmationModalElement') digisafeActionConfirmationModalElement!: ElementRef;

    // Existing properties (unchanged unless specified)
    pendingList: DigisafeReport[] = [];
    approvedList: DigisafeReport[] = [];
    rejectedList: DigisafeReport[] = [];
    digisafeActionConfirmationModalInstance: Modal | null = null;
    itemToConfirmAction: DigisafeReport | null = null;
    actionToConfirm: 'accept' | 'reject' | 'pending' | 'approve' | 'reject-approved' | null = null;
    modalTitle = '';
    modalMessage = '';
    confirmButtonText = '';
    confirmButtonClass = '';
    confirmIconClass = '';
    rejectSubmitted: boolean = false;
    isLoading: boolean = false;
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;
    readonly tabs = [
        { title: 'Pending', status: DigisafeStatus.Pending, listKey: 'pendingList' as const },
        { title: 'Approved', status: DigisafeStatus.Approved, listKey: 'approvedList' as const },
        { title: 'Rejected', status: DigisafeStatus.Rejected, listKey: 'rejectedList' as const },
    ];
    selectedTabIndex = 0;
    isFilterModalOpen = false;
    isCreateModalOpen = false;
    isEditModalOpen = false;
    filters: DigisafeFilter = { /* original filters */
        plantIds: null,
        sort: 'updatedTimestamp,DESC',
        enabled: null,
        id: null,
        fromDate: null,
        toDate: null,
        clusterId: null,
        opcoId: null,
        plantTypeId: null
    };
    currentUserRole: string = '';
    loggedInAdminId: number | null = null;
    loggedInPlantIds: number[] = [];
    loggedInUserDepartmentId: number | null = null; // Added property for user's department ID
    availablePlants: Plant[] = [];
    availablePlantsForCreate: Plant[] = [];
    availableClusters: any[] = [];
    availableCompanies: any[] = [];
    availablePlantTypes: any[] = [];
    isAllPlantsSelected: boolean = false;
    createLoading = false;
    newDigisafeData: DigisafeFormData = this.getInitialFormData();
    editLoading = false;
    // MODIFIED: Use DigisafeFormData for edit form binding
    editingFormData: DigisafeFormData | null = null;
    originalEditReport: DigisafeReport | null = null; // To hold original non-editable data for edit

    acceptingReportId: number | null = null;
    rejectingReportId: number | null = null;

    // ADDED: Property for month/year dropdown options
    availableMonthsYears: MonthYearOption[] = [];
    // ADDED: Property for unique years (optional, can be derived in template)
    uniqueYearsForCreate: number[] = [];
    // ADDED: Property for months filtered by selected year
    filteredMonthsForCreate: MonthYearOption[] = [];

    // Original Service Injections (unchanged)
    private plantService = inject(PlantManagementService);
    private digiSafeService = inject(DigisafeService);
    private updateService = inject(UpdateService);
    private cdr = inject(ChangeDetectorRef);
    private clusterService = inject(ClusterService);
    private opcoService = inject(OpcoService);
    private plantTypeService = inject(PlantTypeService);

    constructor() { }

    ngOnInit(): void {
        console.log("ManageDigisafeComponent initialized");
        this.setCurrentUserRoleAndDetailsById(); // Sets role needed for month/year options
        this.populateAvailableMonthsYears(); // Populate month/year based on role
        this.loadInitialData(); // Fetches other dropdowns and list data
    }

    ngAfterViewInit(): void {
        if (this.digisafeActionConfirmationModalElement) {
            this.digisafeActionConfirmationModalInstance = new Modal(this.digisafeActionConfirmationModalElement.nativeElement);
        } else { console.error("DigiSafe action confirmation modal element not found!"); }
    }

    ngOnDestroy(): void {
        this.digisafeActionConfirmationModalInstance?.dispose();
        console.log("ManageDigisafeComponent destroyed");
    }

    // --- Helper function for safe number conversion ---
    private safeNumber(value: any): number {
        const num = Number(value);
        return isNaN(num) ? 0 : num;
    }

    private setCurrentUserRoleAndDetailsById(): void {
        // Keep original implementation
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                console.error("User data not found or is invalid in localStorage."); this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = []; this.toast?.showErrorToast("User session invalid. Please log in again."); return;
            }
            const currentUser = JSON.parse(userString); this.loggedInAdminId = currentUser?.id ?? null;
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0) ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
            // Retrieve and store the user's department ID
            this.loggedInUserDepartmentId = currentUser?.departmentId ?? null;
            console.log(`User Department ID: ${this.loggedInUserDepartmentId || 'None'}`); // Log the department ID
            const roleId = currentUser?.adminsRoleId; if (roleId === 1) { this.currentUserRole = this.componentRoles.SUPER_ADMIN; }
            else if (roleId === 2) { this.currentUserRole = this.componentRoles.PLANT_ADMIN; if (this.loggedInPlantIds.length === 0) { console.error(`Plant Admin (Role ID ${roleId}, User ID ${this.loggedInAdminId}) has no plants assigned!`); this.toast?.showErrorToast("User configuration error: Plant Admin has no assigned plants."); } }
            else { console.error(`Invalid or missing adminsRoleId (${roleId}) for User ID ${this.loggedInAdminId}.`); this.currentUserRole = ''; this.toast?.showErrorToast("User configuration error: Invalid role."); }
            console.log(`User Role Determined: ${this.currentUserRole || 'None'}, Plant IDs: [${this.loggedInPlantIds.join(', ')}]`);
        } catch (error) { console.error("Error parsing user data from localStorage:", error); this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = []; this.toast?.showErrorToast("Error reading user session. Please log in again."); }
    }

    async loadInitialData(): Promise<void> {
        // Keep original implementation
        await Promise.all([ this.getPlants(), this.getClusters(), this.getCompanies(), this.getPlantTypes() ]);
        await this.loadDigisafeData();
    }

    getCurrentListData(): DigisafeReport[] | undefined {
        // Keep original implementation
        const currentTab = this.tabs[this.selectedTabIndex]; if (currentTab && currentTab.listKey) { const list = (this as any)[currentTab.listKey]; if (Array.isArray(list)) { return list; } } console.warn("Could not get list data for download for tab index:", this.selectedTabIndex); return undefined;
    }

    async fetchAllFilteredDigisafeReports(): Promise<DigisafeReport[] | null> {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab) {
            console.error("Cannot fetch all data: Invalid tab selected.");
            return null;
        }

        const currentStatus = currentTab.status;
        const filterParams: string[] = [`status||eq||${currentStatus}`];

        // Add enabled filter
        if (this.filters.enabled !== null) {
            filterParams.push(`enabled||eq||${this.filters.enabled}`);
        }

        // Add plantIds filter, respecting PLANT_ADMIN role
        const selectedPlantIds = this.filters.plantIds || [];
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            const allowedPlantIds = this.loggedInPlantIds;
            let effectivePlantIds: number[] = selectedPlantIds.length > 0 ? selectedPlantIds.filter(id => allowedPlantIds.includes(id)) : allowedPlantIds;
            if (effectivePlantIds.length > 0) {
                filterParams.push(`plant.id||$in||${effectivePlantIds.join(',')}`);
            } else {
                // If PLANT_ADMIN has no assigned plants or selected plants are not in assigned plants,
                // filter by a non-existent plant ID to return no results.
                filterParams.push(`plant.id||eq||-1`);
            }
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            if (selectedPlantIds.length > 0) {
                filterParams.push(`plant.id||$in||${selectedPlantIds.join(',')}`);
            }
            // If SUPER_ADMIN has no plantIds selected, no plant filter is applied,
            // allowing them to see all plants (subject to other filters).
        }

        // Add date range filter for createdTimestamp
        if (this.filters.fromDate && this.filters.toDate) {
            try {
                const fromDate = new Date(this.filters.fromDate);
                const toDate = new Date(this.filters.toDate);

                // Set time to start of day for fromDate and end of day for toDate
                fromDate.setHours(0, 0, 0, 0);
                toDate.setHours(23, 59, 59, 999);

                // Ensure valid dates before adding filters
                if (!isNaN(fromDate.getTime()) && !isNaN(toDate.getTime())) {
                    filterParams.push(`createdTimestamp||$gte||${fromDate.toISOString()}`);
                    filterParams.push(`createdTimestamp||$lte||${toDate.toISOString()}`);
                    console.log(`Excel download: Filtering by date range: ${fromDate.toISOString()} to ${toDate.toISOString()}`);
                } else {
                    console.warn("Invalid date filters provided for Excel download:", this.filters.fromDate, this.filters.toDate);
                    this.toast?.showErrorToast("Invalid date filter values.");
                }
            } catch (error) {
                console.error("Error processing date filters for Excel download:", error);
                this.toast?.showErrorToast("Error applying date filters for Excel download.");
            }
        } else if (this.filters.fromDate) {
            try {
                const fromDate = new Date(this.filters.fromDate);
                fromDate.setHours(0, 0, 0, 0);
                if (!isNaN(fromDate.getTime())) {
                    filterParams.push(`createdTimestamp||$gte||${fromDate.toISOString()}`);
                    console.log(`Excel download: Filtering by fromDate: ${fromDate.toISOString()}`);
                } else {
                    console.warn("Invalid fromDate filter provided for Excel download:", this.filters.fromDate);
                    this.toast?.showErrorToast("Invalid 'From Date' filter value.");
                }
            } catch (error) {
                console.error("Error processing fromDate filter for Excel download:", error);
                this.toast?.showErrorToast("Error applying 'From Date' filter for Excel download.");
            }
        } else if (this.filters.toDate) {
            try {
                const toDate = new Date(this.filters.toDate);
                toDate.setHours(23, 59, 59, 999);
                if (!isNaN(toDate.getTime())) {
                    filterParams.push(`createdTimestamp||$lte||${toDate.toISOString()}`);
                    console.log(`Excel download: Filtering by toDate: ${toDate.toISOString()}`);
                } else {
                    console.warn("Invalid toDate filter provided for Excel download:", this.filters.toDate);
                    this.toast?.showErrorToast("Invalid 'To Date' filter value.");
                }
            } catch (error) {
                console.error("Error processing toDate filter for Excel download:", error);
                this.toast?.showErrorToast("Error applying 'To Date' filter for Excel download.");
            }
        }

        // Add clusterId filter
        if (this.filters.clusterId !== null && this.filters.clusterId !== undefined) {
            filterParams.push(`plant.clusterId||eq||${this.filters.clusterId}`);
            console.log("Excel download: Filtering by Cluster ID:", this.filters.clusterId);
        }

        // Add opcoId filter
        if (this.filters.opcoId !== null && this.filters.opcoId !== undefined) {
            filterParams.push(`plant.opcoId||eq||${this.filters.opcoId}`);
            console.log("Excel download: Filtering by OpCo ID:", this.filters.opcoId);
        }

        // Add plantTypeId filter
        if (this.filters.plantTypeId !== null && this.filters.plantTypeId !== undefined) {
            filterParams.push(`plant.plantTypeId||eq||${this.filters.plantTypeId}`);
            console.log("Excel download: Filtering by Plant Type ID:", this.filters.plantTypeId);
        }

        const dataRequest = {
            limit: 10000,
            sort: this.filters.sort || 'createdTimestamp,DESC',
            filter: filterParams,
            join: ['plant', 'updated', 'approve', 'rejected']
        };

        try {
            const axiosConfig = createAxiosConfig(dataRequest);
            console.log("Download All Request:", JSON.stringify(axiosConfig, null, 2));
            const response = await this.digiSafeService.getDigisafe(axiosConfig);
            return response?.data ?? response ?? [];
        } catch (error: any) {
            console.error("Error fetching all digisafe reports for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        }
    }

    async downloadExcel(type: 'current' | 'all') {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab || this.isDownloadingExcel) {
            return;
        }

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} reports...`);

        let dataToExport: DigisafeReport[] | null = null;

        try {
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredDigisafeReports();
            } else {
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) dataToExport = null;
            }

            if (dataToExport === null) {
                this.toast?.showErrorToast("Failed to retrieve data for download.");
                return;
            }

            if (!dataToExport || dataToExport.length === 0) {
                this.toast?.showErrorToast(`No reports available to download.`);
                return;
            }

            // Map data for Excel export with all fields
            const dataForExcel = dataToExport.map(item => ({
                'Plant Name': item.plant?.name || 'N/A',
                'Status': this.getStatusText(item.status),
                'Month and Year': this.formatDate(item.createdTimestamp, 'MMM YYYY'),
                'Created Date': item.createdTimestamp ? new Date(item.createdTimestamp).toLocaleDateString() : 'N/A',
                'Created Time': item.createdTimestamp ? new Date(item.createdTimestamp).toLocaleTimeString() : 'N/A',
                'Accident Free Period (Current)': item.AfpCurrent || 'N/A',
                'Accident Free Period (Best)': item.AfpBest || 'N/A',
                'Man Hours Worked': item.ManHoursWorked || 'N/A',
                'Man Hours (Own Employees)': item.ManHoursWorkedOwnEmployees || 'N/A',
                'Man Hours (Contractor)': item.ManHoursWorkedConEmployees || 'N/A',
                'Kilometres Covered': item.KmCovered || 'N/A',
                'Occupational Illness (OI)': item.OI || 'N/A',
                'First Aid Case (FAC)': item.FAC || 'N/A',
                'FAC (Emp Onsite)': item.FACCompOnsite || 'N/A',
                'FAC (Emp Offsite)': item.FACCompOfsite || 'N/A',
                'FAC (Con Onsite)': item.FACconOnsite || 'N/A',
                'FAC (Con Offsite)': item.FACconOfsite || 'N/A',
                'Medical Treatment Case (MTC)': item.MTC || 'N/A',
                'MTC (Emp Onsite)': item.MTCCompOnsite || 'N/A',
                'MTC (Emp Offsite)': item.MTCCompOfsite || 'N/A',
                'MTC (Con Onsite)': item.MTCconOnsite || 'N/A',
                'MTC (Con Offsite)': item.MTCconOfsite || 'N/A',
                'MTC (Visitor)': item.MTCthirdPartyVisitor || 'N/A',
                'Reportable Lost Time Injury (R-LTI)': item.RLTI || 'N/A',
                'R-LTI (Emp Onsite)': item.RLTICompOnsite || 'N/A',
                'R-LTI (Emp Offsite)': item.RLTICompOfsite || 'N/A',
                'R-LTI (Con Onsite)': item.RLTIconOnsite || 'N/A',
                'R-LTI (Con Offsite)': item.RLTIconOfsite || 'N/A',
                'R-LTI (Visitor)': item.RLTIthirdPartyVisitor || 'N/A',
                'Fatal': item.Fatal || 'N/A',
                'Fatal (Emp Onsite)': item.FatalCompOnsite || 'N/A',
                'Fatal (Emp Offsite)': item.FatalCompOfsite || 'N/A',
                'Fatal (Con Onsite)': item.FatalconOnsite || 'N/A',
                'Fatal (Con Offsite)': item.FatalconOfsite || 'N/A',
                'Fatal (Visitor)': item.FatalthirdPartyVisitor || 'N/A',
                'Dangerous Occurrence (DO)': item.DangerOccurence || 'N/A',
                'Property Damage': item.propertyDamage || 'N/A',
                'Fire Incident': item.FireIncident || 'N/A',
                'Non-Fire Incident': item.nonFireIncident || 'N/A',
                'Vehicle related Accident': item.VRA || 'N/A',
                'Property Damage Incident Nos.': item.PDI || 'N/A',
                'Leak-Spill Incident': item.LSI || 'N/A',
                'Near miss Incident': item.NearMissIncident || 'N/A',
                'Accident Cost INR (Divided by 1000)': item.AccidentCost || 'N/A',
                'Man Days Lost (MDL)': item.MDL || 'N/A',
                'MDL (Employees)': item.MDLEmployees || 'N/A',
                'MDL (Contractor)': item.MDLCon || 'N/A',
                'Safety Induction (Participants)': item.SafetyInduction || 'N/A',
                'Tool Box Talk (No of batches)': item.TBTBatches || 'N/A',
                'TBT- Participant Nos.': item.TBTno || 'N/A',
                'Safety Training Program Plan (Batches)': item.STP_Plan || 'N/A',
                'Safety Training Program - MTD (Batches)': item.STP_MTD || 'N/A',
                'Employee Trained - Plan (Nos)': item.ET_Plan || 'N/A',
                'Employee Trained - MTD (Nos)': item.ET_MTD || 'N/A',
                'Training Man-Hours (MTD)': item.TrainingManHours || 'N/A',
                'Training Hours (Emp)': item.TrainingManHoursEmployees || 'N/A',
                'Training Hours (Con)': item.TrainingManHoursCon || 'N/A',
                'Safety Committee Meetings (Plan)': item.SCMPlan || 'N/A',
                'Safety Committee Meetings (MTD)': item.SCM_MTD || 'N/A',
                'Permit To Work Audit (Plan)': item.PWAPlan || 'N/A',
                'Permit To Work Audit (MTD)': item.PWA_MTD || 'N/A',
                'Self Assessment (Plan)': item.SA_Plan || 'N/A',
                'Self Assessment (MTD)': item.SA_MTD || 'N/A',
                'Safety Inspection (Plan)': item.SI_Plan || 'N/A',
                'Safety Inspection (MTD)': item.SI_MTD || 'N/A',
                'Safety-Item / Equipment Inspection (Plan)': item.EI_Plan || 'N/A',
                'Safety-Item / Equipment Inspection (MTD)': item.EI_MTD || 'N/A',
                'Safety Promotional Campaign (Plan)': item.SPC_Plan || 'N/A',
                'Safety Promotional Campaign (MTD)': item.SPC_MTD || 'N/A',
                'Emergency Mock Drill (Plan)': item.EMD_Plan || 'N/A',
                'Emergency Mock Drill (MTD)': item.EMD_MTD || 'N/A',
                'Industrial Hygiene Monitoring (Plan)': item.IHM_Plan || 'N/A',
                'Industrial Hygiene Monitoring (MTD)': item.IHM_MTD || 'N/A',
                'Safety Notification Raised': item.SnRaised || 'N/A',
                'Safety Notification Closed': item.SnClosed || 'N/A',
                'Safety Notification Open': item.SnOpen || 'N/A',
                'Safety Notification Open > 90 Days': item.SnOpen90days || 'N/A',
                'Safety Walkthrough by Senior leadership Team': item.SafetyWalkthrough || 'N/A',
                'RWC (Total)': item.RWC || 'N/A',
                'RWC (Emp Onsite)': item.RWCCompOnsite || 'N/A',
                'RWC (Emp Offsite)': item.RWCCompOfsite || 'N/A',
                'RWC (Con Onsite)': item.RWCconOnsite || 'N/A',
                'RWC (Con Offsite)': item.RWCconOfsite || 'N/A',
                'MWD (Total)': item.MWD || 'N/A',
                'MWD (Employee)': item.MWDEmployee || 'N/A',
                'MWD (Con Onsite)': item.MWDContractorOnsite || 'N/A',
                'Submitted By': item.updated ? `${item.updated.firstName || ''} ${item.updated.lastName || ''}`.trim() || 'N/A' : 'N/A',
                'Submitted By Contact': item.updated?.contactNumber || 'N/A',
                'Submitted By Email': item.updated?.email || 'N/A',
                'Approved By': item.approve ? `${item.approve.firstName || ''} ${item.approve.lastName || ''}`.trim() || 'N/A' : 'N/A',
                'Approved Date': item.approvedTimestamp ? new Date(item.approvedTimestamp).toLocaleDateString() : (item.updatedTimestamp ? new Date(item.updatedTimestamp).toLocaleDateString() : 'N/A'),
                'Rejected By': item.rejected ? `${item.rejected.firstName || ''} ${item.rejected.lastName || ''}`.trim() || 'N/A' : 'N/A',
                'Rejected Date': item.rejectedTimestamp ? new Date(item.rejectedTimestamp).toLocaleDateString() : (item.updatedTimestamp ? new Date(item.updatedTimestamp).toLocaleDateString() : 'N/A'),
                'Rejection Reason': item.rejectionReason || 'N/A'
            }));

            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            const safeSheetName = currentTab.title.replace(/[^a-zA-Z0-9]/g, '').substring(0, 30);
            XLSX.utils.book_append_sheet(wb, ws, safeSheetName || 'DigiSafe');

            // Auto-size columns (optional enhancement)
            const colWidths = [];
            for (let i = 0; i < Object.keys(dataForExcel[0] || {}).length; i++) {
                colWidths.push({ wch: 20 }); // Set default width
            }
            ws['!cols'] = colWidths;

            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'AllFiltered';
            const fileName = `DigiSafe_${safeSheetName}_${typeStr}_${dateStr}.xlsx`;

            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);
        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // Helper method to convert status code to text
    private getStatusText(status: DigisafeStatus): string {
        switch (status) {
            case DigisafeStatus.Pending:
                return 'Pending';
            case DigisafeStatus.Approved:
                return 'Approved';
            case DigisafeStatus.Rejected:
                return 'Rejected';
            default:
                return 'Unknown';
        }
    }

    // --- MODIFIED: Initial Form Data ---
    getInitialFormData(): DigisafeFormData {
        // Initialize all fields as null, including calculated fields
        return {
            plantId: null, selectedYear: null, selectedMonth: null, AfpCurrent: null, AfpBest: null, ManHoursWorked: null, KmCovered: null, OI: null, FAC: null, MTC: null, RLTI: null, Fatal: null, DangerOccurence: null, FireIncident: null, VRA: null, PDI: null, LSI: null, AccidentCost: null, MDL: null, NearMissIncident: null, SafetyInduction: null, TBTBatches: null, TBTno: null, STP_Plan: null, STP_MTD: null, ET_Plan: null, ET_MTD: null, TrainingManHours: null, SCMPlan: null, SCM_MTD: null, PWAPlan: null, PWA_MTD: null, SA_Plan: null, SA_MTD: null, SI_Plan: null, SI_MTD: null, EI_Plan: null, EI_MTD: null, SPC_Plan: null, SPC_MTD: null, EMD_Plan: null, EMD_MTD: null, IHM_Plan: null, IHM_MTD: null, SnRaised: null, SnClosed: null, SnOpen: null, SnOpen90days: null, SafetyWalkthrough: null, ManHoursWorkedOwnEmployees: null, ManHoursWorkedConEmployees: null, FACCompOnsite: null, FACCompOfsite: null, FACconOnsite: null, FACconOfsite: null, MTCCompOnsite: null, MTCCompOfsite: null, MTCconOnsite: null, MTCconOfsite: null, MTCthirdPartyVisitor: null, RLTICompOnsite: null, RLTICompOfsite: null, RLTIconOnsite: null, RLTIconOfsite: null, RLTIthirdPartyVisitor: null, FatalCompOnsite: null, FatalCompOfsite: null, FatalconOnsite: null, FatalconOfsite: null, FatalthirdPartyVisitor: null, propertyDamage: null, nonFireIncident: null, MDLEmployees: null, MDLCon: null, TrainingManHoursEmployees: null, TrainingManHoursCon: null, RWC: null, RWCCompOnsite: null, RWCCompOfsite: null, RWCconOnsite: null, RWCconOfsite: null, MWD: null, MWDEmployee: null, MWDContractorOnsite: null
        };
    }

    async getPlants(): Promise<void> {
    const plantParams: any = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };

    // Add cluster filter if clusterId is selected
    if (this.filters.clusterId !== null && this.filters.clusterId !== undefined) {
      plantParams.filter.push(`clusterId||eq||${this.filters.clusterId}`);
      console.log("Filtering plants by Cluster ID:", this.filters.clusterId);
    }

    let allEnabledPlants: Plant[] = [];
    try {
      const axiosConfig = createAxiosConfig(plantParams);
      const response = await this.plantService.getPlants(axiosConfig);
      allEnabledPlants = response?.data ?? response ?? [];

      if (!Array.isArray(allEnabledPlants)) {
        console.error("Received non-array response for plants:", allEnabledPlants);
        allEnabledPlants = [];
      }
    } catch (error) {
      console.error("Error fetching plants:", error);
      this.availablePlants = [];
      // Assuming a toast service is available or implement simple console error
      // this.toast?.showErrorToast('Failed to load plant list.');
    }

    // Filter plants for PLANT_ADMIN based on assigned plant IDs
    if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
      console.log("Filtering plants for PLANT_ADMIN. Allowed Plant IDs:", this.loggedInPlantIds);
      allEnabledPlants = allEnabledPlants.filter(plant => this.loggedInPlantIds.includes(plant.id));
      console.log("Filtered plants for PLANT_ADMIN:", allEnabledPlants.map(p => p.name));
    }

    this.availablePlants = allEnabledPlants;
    // Reset plant multi-select if available plants change significantly
    this.filters.plantIds = null;
    this.isAllPlantsSelected = false;
  }

  // Method to handle cluster filter change
  onClusterChange(clusterId: number | null): void {
    this.filters.clusterId = clusterId;
    this.getPlants(); // Fetch plants based on the new cluster filter
  }

  async getClusters(): Promise<void> {
    console.log('Fetching clusters...');
    try {
      const clusterParams = { sort:'title,ASC', filter:['enabled||eq||true'], limit: 1000 };
      const axiosConfig = createAxiosConfig(clusterParams);
      const response = await this.clusterService.getCluster(axiosConfig);
      this.availableClusters = response?.data ?? response ?? [];
      console.log('Clusters loaded:', this.availableClusters);
    } catch (error) {
      console.error("Error fetching clusters:", error);
      this.availableClusters = [];
      // this.toast?.showErrorToast('Failed to load cluster list.');
    }
  }

  async getCompanies(): Promise<void> {
    console.log('Fetching companies (OpCos)...');
    try {
      const companyParams = { sort:'title,ASC', filter:['enabled||eq||true'], limit: 1000 };
      const axiosConfig = createAxiosConfig(companyParams);
      const response = await this.opcoService.getOpco(axiosConfig);
      this.availableCompanies = response?.data ?? response ?? [];
      console.log('Companies loaded:', this.availableCompanies);
    } catch (error) {
      console.error("Error fetching companies:", error);
      this.availableCompanies = [];
      // this.toast?.showErrorToast('Failed to load company list.');
    }
  }

  async getPlantTypes(): Promise<void> {
    console.log('Fetching plant types...');
    try {
      const plantTypeParams = { sort:'title,ASC', filter:['enabled||eq||true'], limit: 1000 };
      const axiosConfig = createAxiosConfig(plantTypeParams);
      const response = await this.plantTypeService.getPlantType(axiosConfig);
      this.availablePlantTypes = response?.data ?? response ?? [];
      console.log('Plant types loaded:', this.availablePlantTypes);
    } catch (error) {
      console.error("Error fetching plant types:", error);
      this.availablePlantTypes = [];
      // this.toast?.showErrorToast('Failed to load plant type list.');
    }
  }

    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; }

    // --- MODIFIED: Open Create Modal ---
    async openCreateModal(): Promise<void> {
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length === 0) {
            this.toast?.showErrorToast("Cannot create report: You are not assigned to any plants."); return;
        }

        this.populateAvailableMonthsYears(); // Ensure options are fresh
        this.newDigisafeData = this.getInitialFormData();

        // Set current year for PLANT_ADMIN
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            this.newDigisafeData.selectedYear = new Date().getFullYear();
            // For PLANT_ADMIN, month selection is still needed, but year is fixed.
            // We still need availableMonthsYears for the month dropdown filtering.
            // The uniqueYearsForCreate and onYearSelectForCreate logic below is still needed
            // for the SUPER_ADMIN case, so we won't remove it entirely, but it won't
            // affect the PLANT_ADMIN's pre-set year.
        }


        this.availablePlantsForCreate = [];

        if (this.availablePlants.length === 0) { await this.getPlants(); }

        // Assign available plants based on role
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            this.availablePlantsForCreate = this.availablePlants.filter(plant => this.loggedInPlantIds.includes(plant.id));
            if (this.availablePlantsForCreate.length === 1) { this.newDigisafeData.plantId = this.availablePlantsForCreate[0].id; }
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            this.availablePlantsForCreate = this.availablePlants;
        } else { this.toast?.showErrorToast("Permission denied to create reports."); return; }

        this.calculateTotals(this.newDigisafeData); // Calculate initial totals (will be 0)

        // Get unique years for the dropdown (needed for SUPER_ADMIN)
        this.uniqueYearsForCreate = [...new Set(this.availableMonthsYears.map(opt => opt.year))].sort((a, b) => b - a); // Sort descending

        // Call onYearSelectForCreate initially to populate filteredMonthsForCreate
        // This is needed for both roles to populate the month dropdown correctly based on the selected/pre-set year.
        this.onYearSelectForCreate();

        setTimeout(() => this.createForm?.resetForm(this.newDigisafeData), 0);
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }
    closeCreateModal(): void { this.isCreateModalOpen = false; }

    // --- MODIFIED: Submit Create Form ---
    async submitCreateForm(): Promise<void> {
        // Mark all fields as touched to trigger validation display
        this.createForm?.form.markAllAsTouched();

        // Force change detection to update validation display
        this.cdr.detectChanges();

        // Check if form is invalid - if so, prevent submission and let field-level errors show
        if (this.createForm?.invalid) {
            // Don't show generic toast - let the individual field error messages show
            return;
        }

        // Additional validation for Month/Year selection (these might not be caught by form validation)
        if (!this.newDigisafeData.selectedMonth || !this.newDigisafeData.selectedYear) {
            this.toast?.showErrorToast("Please select the Month and Year for the report.");
            return;
        }

        const selectedPlantId = this.newDigisafeData.plantId;
        if (!selectedPlantId) {
            this.toast?.showErrorToast("Please select a Plant.");
            return;
        }
        // Role validation - fixed to properly check plant admin permissions
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            // Convert selectedPlantId to number to ensure consistent type comparison
            const plantIdNum = Number(selectedPlantId);
            // Convert loggedInPlantIds to numbers as well for consistent comparison
            const loggedInPlantIdsNum = this.loggedInPlantIds.map(id => Number(id));

            if (!loggedInPlantIdsNum.includes(plantIdNum)) {
                this.toast?.showErrorToast("You do not have permission to create reports for the selected plant.");
                console.error(`Security Violation: Plant Admin ${this.loggedInAdminId} attempting to create report for unauthorized plant ${selectedPlantId}`);
                return;
            }
        } else if (this.currentUserRole !== this.componentRoles.SUPER_ADMIN) {
            this.toast?.showErrorToast("You do not have permission to create reports.");
            return;
        }

        this.createLoading = true;
        const formValues = { ...this.newDigisafeData };
        const payload: any = {};
        payload.plantId = Number(formValues.plantId);

        this.calculateTotals(formValues); // Recalculate totals before sending

        const allFormKeys = Object.keys(this.getInitialFormData()) as Array<keyof DigisafeFormData>;
        for (const key of allFormKeys) {
            if (key !== 'plantId' && key !== 'selectedMonth' && key !== 'selectedYear') { // Exclude selection fields
                const value = formValues[key];
                // Send numeric fields as strings (based on original code's pattern)
                payload[key] = (value === null || value === undefined || (typeof value === 'string' && value === '')) ? null : String(value);
            }
        }
        // ADDED: Include selected month and year in the payload
        if (formValues.selectedMonth !== null && formValues.selectedYear !== null) {
             payload['month'] = String(formValues.selectedMonth);
             payload['year'] = String(formValues.selectedYear);
        }

        payload['createdBy'] = this.loggedInAdminId;
        payload['status'] = DigisafeStatus.Pending;
        // Do NOT send selectedMonth/Year unless backend explicitly requires it for setting createdTimestamp

        console.log("Create Payload:", JSON.stringify(payload, null, 2));

        try {
            const response = await this.digiSafeService.createDigisafe(payload);

            // Check if response contains an error code
            if (response && response.responseCode && response.responseCode !== 200) {
                console.error("Error creating report (from response):", response);
                this.toast?.showErrorToast(response.message || 'Failed to add report.');
                return;
            }

            this.toast?.showSuccessToast('DigiSafe report created successfully!');
            this.closeCreateModal();
            this.currentPage = 1;
            if (this.selectedTabIndex !== 0) {
                this.selectedTabIndex = 0;
                await this.loadDigisafeData();
            } else {
                await this.loadDigisafeData();
            }
        } catch (error: any) {
            console.error("Error creating report:", error);
            let errorMessage = 'Failed to add report.';
            if (error && error.responseCode) {
                errorMessage = error.message || errorMessage;
            } else if (error && typeof error === 'object') {
                errorMessage = error.message || (error.response && error.response.message) || (error.response && error.response.data && error.response.data.message) || errorMessage;
            }
            this.toast?.showErrorToast(errorMessage);
        }
        finally {
            this.createLoading = false;
        }
    }

    // --- MODIFIED: Open Edit Modal ---
    openEditModal(item: DigisafeReport): void {
        // For super admin, allow editing regardless of status
        if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            // Super admin can edit any record regardless of status
            console.log("Super admin editing record with status:", this.getStatusText(item.status));
        } else if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            // Plant admin can only edit records for their assigned plants
            // Convert plantId to number to ensure consistent type comparison
            const plantIdNum = Number(item.plantId);
            // Convert loggedInPlantIds to numbers as well for consistent comparison
            const loggedInPlantIdsNum = this.loggedInPlantIds.map(id => Number(id));

            if (!plantIdNum || !loggedInPlantIdsNum.includes(plantIdNum)) {
                this.toast?.showErrorToast("You do not have permission to edit this report.");
                console.error(`Security Violation: Plant Admin ${this.loggedInAdminId} attempting to open edit modal for unauthorized plant ${item.plantId}`);
                return;
            }

            // Plant admin can only edit pending records
            if (item.status !== DigisafeStatus.Pending) {
                this.toast?.showErrorToast("You do not have permission to edit approved or rejected reports.");
                console.error(`Security Violation: Plant Admin ${this.loggedInAdminId} attempting to edit non-pending report with status ${item.status}`);
                return;
            }
        }
        console.log("Opening edit modal for report ID:", item.id);

        this.originalEditReport = JSON.parse(JSON.stringify(item)); // Store original
        this.editingFormData = this.getInitialFormData(); // Start with fresh form data structure

        // Populate editingFormData from originalEditReport, keeping values as strings for pattern validation
        const formKeys = Object.keys(this.editingFormData) as Array<keyof DigisafeFormData>;
        formKeys.forEach(key => {
            if (key !== 'selectedMonth' && key !== 'selectedYear') { // Skip selection fields
                const reportKey = key as keyof DigisafeReport;
                if (this.originalEditReport!.hasOwnProperty(reportKey)) {
                    const value = this.originalEditReport![reportKey];
                    // Keep values as strings for pattern validation
                    if (value !== null && value !== undefined) {
                        (this.editingFormData as any)[key] = String(value);
                    } else {
                        (this.editingFormData as any)[key] = value;
                    }
                }
            }
        });

        // Set display month/year from original report (non-editable in this version)
        if (this.originalEditReport && this.originalEditReport.createdTimestamp) {
            try {
                const createdDate = new Date(this.originalEditReport.createdTimestamp);
                this.editingFormData.selectedYear = createdDate.getFullYear();
                this.editingFormData.selectedMonth = createdDate.getMonth() + 1; // Month is 1-12
            } catch (e) { console.error("Error parsing createdTimestamp for edit:", e); }
        }

        this.calculateTotals(this.editingFormData); // Calculate totals for display

        setTimeout(() => {
            if (this.editForm && this.editingFormData) {
                this.editForm.resetForm(this.editingFormData); // Reset form with populated data

                // Force form validation to be re-evaluated after the form is populated
                setTimeout(() => {
                    if (this.editForm) {
                        this.editForm.form.markAllAsTouched();
                        this.editForm.form.updateValueAndValidity();
                        this.cdr.detectChanges();
                    }
                }, 100);
            }
        }, 0);

        this.isEditModalOpen = true;
        this.editLoading = false;
    }
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.editingFormData = null;
        this.originalEditReport = null;
    }

    // --- MODIFIED: Submit Edit Form ---
    async submitEditForm(): Promise<void> {
        // Force form validation to be re-evaluated
        this.editForm?.form.markAllAsTouched();
        this.editForm?.form.updateValueAndValidity();
        this.cdr.detectChanges();

        // Check if form is valid
        if (this.editForm?.invalid || !this.editingFormData || !this.originalEditReport) {
            console.error("Form validation failed:", this.editForm?.invalid, "Form value:", this.editForm?.value);
            this.toast?.showErrorToast("Please fill all required fields or invalid state."); return;
        }
        // For super admin, allow editing regardless of status
        if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            // Super admin can edit any record regardless of status
            console.log("Super admin saving edits for record with status:", this.getStatusText(this.originalEditReport.status));
        } else if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            // Plant admin can only edit records for their assigned plants
            // Convert plantId to number to ensure consistent type comparison
            const plantIdNum = Number(this.editingFormData.plantId);
            // Convert loggedInPlantIds to numbers as well for consistent comparison
            const loggedInPlantIdsNum = this.loggedInPlantIds.map(id => Number(id));

            if (!plantIdNum || !loggedInPlantIdsNum.includes(plantIdNum)) {
                this.toast?.showErrorToast("Permission denied to save changes for this report.");
                console.error(`Security Violation: Plant Admin ${this.loggedInAdminId} attempting to edit report for unauthorized plant ${this.editingFormData.plantId}`);
                return;
            }

            // Plant admin can only edit pending records
            if (this.originalEditReport.status !== DigisafeStatus.Pending) {
                this.toast?.showErrorToast("You do not have permission to edit approved or rejected reports.");
                console.error(`Security Violation: Plant Admin ${this.loggedInAdminId} attempting to save edits for non-pending report with status ${this.originalEditReport.status}`);
                return;
            }
        }

        this.editLoading = true;
        this.calculateTotals(this.editingFormData); // Recalculate totals based on form changes

        const dataPayload: { [key: string]: any } = {};
        const formValues = this.editForm?.value || {}; // Get current form values
        const originalReport = this.originalEditReport;

        // Iterate through keys defined in the form data interface
        const formKeys = Object.keys(this.getInitialFormData()) as Array<keyof DigisafeFormData>;
        for (const key of formKeys) {
            // Exclude selection fields and potentially non-editable fields like plantId
            if (key === 'selectedMonth' || key === 'selectedYear' || key === 'plantId') continue;

            let value: any;
            // Use the calculated value for total fields from editingFormData
            if (['ManHoursWorked', 'FAC', 'MTC', 'RLTI', 'Fatal', 'MDL', 'TrainingManHours', 'RWC', 'MWD'].includes(key)) {
                 value = this.editingFormData[key];
            } else if (formValues.hasOwnProperty(key)) {
                 value = formValues[key]; // Use value from the Angular form for other fields
            } else {
                 continue; // Should not happen if form is reset correctly
            }

            // Convert to string if needed by API (based on create logic)
            const numericAsStringKeys = new Set<string>([ /* Copy the set from submitCreateForm */ ]); // Copy the same set here
            if (numericAsStringKeys.has(key)) {
                dataPayload[key] = (value === null || value === undefined || value === '') ? null : String(value);
            } else {
                dataPayload[key] = value;
            }
        }
        // Do NOT send plantId, status, createdBy, createdTimestamp

        const apiPayload = {
            tableName: "digisafe",
            id: Number(originalReport.id),
            data: dataPayload,
        };

        console.log(`Submitting Edit. Final API Payload:`, JSON.stringify(apiPayload, null, 2));

        try {
            const response = await this.updateService.update(apiPayload);
            // Check if response contains an error code
            if (response.responseCode !== 200) {
                console.log("update response",response.message);
                console.log("error update digisafe")
                this.toast?.showErrorToast(response.message || 'Failed to update report.');
                return;
            }

            this.toast?.showSuccessToast('Report updated successfully!');
            this.closeEditModal();
            await this.loadDigisafeData();
        } catch (error: any) {
            console.error(`Error updating report ID ${originalReport.id}:`, error);
            let errorMessage = 'Failed to update report.';
            if (error && error.responseCode) {
                errorMessage = error.message || errorMessage;
            } else if (error && typeof error === 'object') {
                errorMessage = error.message || (error.response && error.response.message) || (error.response && error.response.data && error.response.data.message) || errorMessage;
            }
            this.toast?.showErrorToast(errorMessage);
        }
        finally {
            this.editLoading = false;
        }
    }

    async loadDigisafeData(): Promise<void> {
        if (this.isLoading) return;
        this.isLoading = true;
        this.clearCurrentList(); // Clear the list for the current tab

        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab) {
            console.error("Invalid tab selected.");
            this.isLoading = false;
            return;
        }

        const currentStatus = currentTab.status;
        const filterParams: string[] = [`status||eq||${currentStatus}`];

        // Add enabled filter
        if (this.filters.enabled !== null) {
            filterParams.push(`enabled||eq||${this.filters.enabled}`);
        }

        // Add plantIds filter, respecting PLANT_ADMIN role
        const selectedPlantIds = this.filters.plantIds || [];
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            const allowedPlantIds = this.loggedInPlantIds;
            let effectivePlantIds: number[] = selectedPlantIds.length > 0 ? selectedPlantIds.filter(id => allowedPlantIds.includes(id)) : allowedPlantIds;
            if (effectivePlantIds.length > 0) {
                filterParams.push(`plant.id||$in||${effectivePlantIds.join(',')}`);
            } else {
                // If PLANT_ADMIN has no assigned plants or selected plants are not in assigned plants,
                // filter by a non-existent plant ID to return no results.
                filterParams.push(`plant.id||eq||-1`);
            }
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            if (selectedPlantIds.length > 0) {
                filterParams.push(`plant.id||$in||${selectedPlantIds.join(',')}`);
            }
            // If SUPER_ADMIN has no plantIds selected, no plant filter is applied,
            // allowing them to see all plants (subject to other filters).
        }

        // Add date range filter for createdTimestamp
        if (this.filters.fromDate && this.filters.toDate) {
            try {
                const fromDate = new Date(this.filters.fromDate);
                const toDate = new Date(this.filters.toDate);

                // Set time to start of day for fromDate and end of day for toDate
                fromDate.setHours(0, 0, 0, 0);
                toDate.setHours(23, 59, 59, 999);

                // Ensure valid dates before adding filters
                if (!isNaN(fromDate.getTime()) && !isNaN(toDate.getTime())) {
                    filterParams.push(`createdTimestamp||$gte||${fromDate.toISOString()}`);
                    filterParams.push(`createdTimestamp||$lte||${toDate.toISOString()}`);
                    console.log(`Filtering by date range: ${fromDate.toISOString()} to ${toDate.toISOString()}`);
                } else {
                    console.warn("Invalid date filters provided:", this.filters.fromDate, this.filters.toDate);
                    this.toast?.showErrorToast("Invalid date filter values.");
                }
            } catch (error) {
                console.error("Error processing date filters:", error);
                this.toast?.showErrorToast("Error applying date filters.");
            }
        } else if (this.filters.fromDate) {
             try {
                const fromDate = new Date(this.filters.fromDate);
                fromDate.setHours(0, 0, 0, 0);
                 if (!isNaN(fromDate.getTime())) {
                    filterParams.push(`createdTimestamp||$gte||${fromDate.toISOString()}`);
                    console.log(`Filtering by fromDate: ${fromDate.toISOString()}`);
                 } else {
                    console.warn("Invalid fromDate filter provided:", this.filters.fromDate);
                    this.toast?.showErrorToast("Invalid 'From Date' filter value.");
                 }
             } catch (error) {
                console.error("Error processing fromDate filter:", error);
                this.toast?.showErrorToast("Error applying 'From Date' filter.");
             }
        } else if (this.filters.toDate) {
             try {
                const toDate = new Date(this.filters.toDate);
                toDate.setHours(23, 59, 59, 999);
                 if (!isNaN(toDate.getTime())) {
                    filterParams.push(`createdTimestamp||$lte||${toDate.toISOString()}`);
                    console.log(`Filtering by toDate: ${toDate.toISOString()}`);
                 } else {
                    console.warn("Invalid toDate filter provided:", this.filters.toDate);
                    this.toast?.showErrorToast("Invalid 'To Date' filter value.");
                 }
             } catch (error) {
                console.error("Error processing toDate filter:", error);
                this.toast?.showErrorToast("Error applying 'To Date' filter.");
             }
        }


        // Add clusterId filter
        if (this.filters.clusterId !== null && this.filters.clusterId !== undefined) {
            filterParams.push(`plant.clusterId||eq||${this.filters.clusterId}`);
            console.log("Filtering by Cluster ID:", this.filters.clusterId);
        }

        // Add opcoId filter
        if (this.filters.opcoId !== null && this.filters.opcoId !== undefined) {
            filterParams.push(`plant.opcoId||eq||${this.filters.opcoId}`);
            console.log("Filtering by OpCo ID:", this.filters.opcoId);
        }

        // Add plantTypeId filter
        if (this.filters.plantTypeId !== null && this.filters.plantTypeId !== undefined) {
            filterParams.push(`plant.plantTypeId||eq||${this.filters.plantTypeId}`);
            console.log("Filtering by Plant Type ID:", this.filters.plantTypeId);
        }

        const dataRequest = {
            page: this.currentPage,
            limit: this.itemsPerPage,
            sort: this.filters.sort || 'createdTimestamp,DESC',
            filter: filterParams,
            join: ['plant', 'updated', 'approve', 'rejected'] // Include necessary joins
        };

        try {
            const axiosConfig = createAxiosConfig(dataRequest);
            console.log("DigiSafe API Request Params:", JSON.stringify(axiosConfig, null, 2));

            const response = await this.digiSafeService.getDigisafe(axiosConfig);

            const listData = response?.data ?? (Array.isArray(response) ? response : []);
            // Fix: Use response.total instead of response.totalItems
            this.totalItems = response?.total ?? response?.totalItems ?? (Array.isArray(response) ? response.length : 0);

            // Debug log to check response structure and totalItems
            console.log("API Response:", response);
            console.log(`Tab: ${currentTab.title}, Total Items: ${this.totalItems}, Items Per Page: ${this.itemsPerPage}, Current Page: ${this.currentPage}`);

            // Assign data to the correct list based on the current tab
            switch (currentTab.status) {
                case DigisafeStatus.Pending:
                    this.pendingList = listData;
                    break;
                case DigisafeStatus.Approved:
                    this.approvedList = listData;
                    break;
                case DigisafeStatus.Rejected:
                    this.rejectedList = listData;
                    break;
                default:
                    console.warn("Unknown tab status:", currentTab.status);
                    break;
            }

        } catch (error: any) {
            console.error("Error fetching digisafe data:", error);
            this.totalItems = 0;
            this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to load DigiSafe data.');
        } finally {
            this.isLoading = false;
            this.cdr.detectChanges(); // Ensure UI updates
        }
    }
    private clearCurrentList(): void { /* Keep original implementation */ }
    onTabSelected(index: number): void {
        console.log('Tab selected:', index);
        this.selectedTabIndex = index;
        this.currentPage = 1; // Reset pagination when changing tabs
        this.loadDigisafeData();
    }
    onPageChange(page: number): void {
        if (this.currentPage !== page) {
            this.currentPage = page;
            this.loadDigisafeData();
        }
    }
    resetFilters(): void {
        console.log('Resetting filters');
        // Reset the filters object to its initial state
        this.filters = {
            plantIds: null,
            sort: 'createdTimestamp,DESC',
            enabled: null,
            id: null,
            fromDate: null,
            toDate: null,
            clusterId: null,
            opcoId: null,
            plantTypeId: null
        };
        this.isAllPlantsSelected = false; // Reset select all checkbox state
        this.closeFilterModal(); // Close the offcanvas
        this.currentPage = 1; // Reset to the first page
        this.loadDigisafeData(); // Load data without filters
    }
    // Handle 'Select All' Plants
    toggleSelectAllPlants(event: Event): void {
      const checkbox = event.target as HTMLInputElement;
      if (checkbox.checked) {
        this.filters.plantIds = this.availablePlants.map(p => p.id);
        this.isAllPlantsSelected = true;
      } else {
        this.filters.plantIds = [];
        this.isAllPlantsSelected = false;
      }
      this.updateSelectAllState(); // Keep checkbox state consistent
    }

    // Update 'Select All' Checkbox State based on individual selections
    updateSelectAllState(): void {
      const numAvailable = this.availablePlants.length;
      const numSelected = this.filters.plantIds?.length ?? 0;

      if (numSelected === 0) {
        this.isAllPlantsSelected = false;
      } else if (numSelected === numAvailable && numAvailable > 0) {
        this.isAllPlantsSelected = true;
      } else {
        this.isAllPlantsSelected = false; // Treat as unchecked for standard checkbox
      }
    }

    applyFilters(): void {
        console.log('Applying filters:', this.filters);
        this.closeFilterModal(); // Close the offcanvas
        this.currentPage = 1; // Reset to the first page when applying new filters
        this.loadDigisafeData(); // Load data with the new filters
    }
    openConfirmationModal(item: DigisafeReport, action: 'accept' | 'reject' | 'pending' | 'approve' | 'reject-approved'): void {
        if (this.acceptingReportId || this.rejectingReportId) return; // Prevent opening if another action is in progress

        this.itemToConfirmAction = item;
        this.actionToConfirm = action;

        const reportDate = this.formatDate(item.createdTimestamp, 'MMM YYYY');
        const plantName = item.plant?.name || 'this plant';

        if (action === 'accept') {
            this.modalTitle = 'Confirm Acceptance';
            this.modalMessage = `Are you sure you want to accept the report for ${plantName} (${reportDate})?`;
            this.confirmButtonText = 'Accept';
            this.confirmButtonClass = 'btn-success';
            this.confirmIconClass = 'bi-check-circle';
            this.rejectSubmitted = false; // Reset validation state
        } else if (action === 'reject') {
            this.modalTitle = 'Confirm Rejection';
            this.modalMessage = `Are you sure you want to reject the report for ${plantName} (${reportDate})?`;
            this.confirmButtonText = 'Reject';
            this.confirmButtonClass = 'btn-danger';
            this.confirmIconClass = 'bi-x-circle';
            this.rejectSubmitted = false; // Reset validation state
        } else if (action === 'pending') {
            // Move to pending state (from approved or rejected)
            this.modalTitle = 'Move to Pending';
            this.modalMessage = `Are you sure you want to move this report for ${plantName} (${reportDate}) back to pending state?`;
            this.confirmButtonText = 'Move to Pending';
            this.confirmButtonClass = 'btn-warning';
            this.confirmIconClass = 'bi-arrow-counterclockwise';
            this.rejectSubmitted = false;
        } else if (action === 'approve') {
            // Move to approved state (from rejected)
            this.modalTitle = 'Move to Approved';
            this.modalMessage = `Are you sure you want to approve this previously rejected report for ${plantName} (${reportDate})?`;
            this.confirmButtonText = 'Approve';
            this.confirmButtonClass = 'btn-success';
            this.confirmIconClass = 'bi-check-circle';
            this.rejectSubmitted = false;
        } else if (action === 'reject-approved') {
            // Move to rejected state (from approved)
            this.modalTitle = 'Move to Rejected';
            this.modalMessage = `Are you sure you want to reject this previously approved report for ${plantName} (${reportDate})?`;
            this.confirmButtonText = 'Reject';
            this.confirmButtonClass = 'btn-danger';
            this.confirmIconClass = 'bi-x-circle';
            this.rejectSubmitted = false;
        } else {
            console.error("Invalid action for confirmation modal:", action);
            return;
        }

        this.digisafeActionConfirmationModalInstance?.show();
    }

    closeConfirmationModal(): void {
        this.digisafeActionConfirmationModalInstance?.hide();
        this.itemToConfirmAction = null;
        this.actionToConfirm = null;
        this.rejectSubmitted = false;
    }

    confirmAction(): void {
        if (!this.itemToConfirmAction || !this.actionToConfirm) { this.toast?.showErrorToast("An internal error occurred."); this.closeConfirmationModal(); return; }

        if (this.actionToConfirm === 'reject' || this.actionToConfirm === 'reject-approved') {
            this.rejectSubmitted = true;
        }

        const itemToAction = this.itemToConfirmAction;
        const action = this.actionToConfirm;

        this.closeConfirmationModal();

        if (action === 'accept') {
            this.executeAcceptReport(itemToAction);
        } else if (action === 'reject') {
            this.executeRejectReport(itemToAction);
        } else if (action === 'pending') {
            this.executeMoveToPending(itemToAction);
        } else if (action === 'approve') {
            this.executeMoveToApproved(itemToAction);
        } else if (action === 'reject-approved') {
            this.executeMoveToRejected(itemToAction);
        }
    }

    async executeAcceptReport(item: DigisafeReport): Promise<void> {
        if (this.acceptingReportId || this.rejectingReportId) return;
        this.acceptingReportId = item.id;

        const payload = {
            tableName: "digisafe",
            id: item.id,
            data: {
                status: DigisafeStatus.Approved,
                approveId: this.loggedInAdminId // Add approveId with current userId
            },
            createdBy: this.loggedInAdminId
        };

        try {
            console.log('Executing API call for ACCEPT:', payload);
            const response = await this.updateService.update(payload);

            // Check if response contains an error code
            if (response && response.responseCode && response.responseCode !== 200) {
                console.error(`Error accepting report ID ${item.id} (from response):`, response);
                this.toast?.showErrorToast(response.message || `Failed to accept report.`);
                return;
            }

            this.toast?.showSuccessToast(`Report ID ${item.id} approved.`);
            await this.loadDigisafeData();
        } catch (error: any) {
            console.error(`Error accepting report ID ${item.id}:`, error);
            // Direct error handling for the format { message: "Failed to save", responseCode: 400 }
            if (error && error.responseCode) {
                this.toast?.showErrorToast(error.message || `Failed to accept report.`);
            } else if (error && typeof error === 'object') {
                // Try to extract message from any error object format
                const errorMessage = error.message ||
                                    (error.response && error.response.message) ||
                                    (error.response && error.response.data && error.response.data.message) ||
                                    `Failed to accept report.`;
                this.toast?.showErrorToast(errorMessage);
            } else {
                this.toast?.showErrorToast(`Failed to accept report.`);
            }
        } finally {
            this.acceptingReportId = null;
        }
    }

    async executeRejectReport(item: DigisafeReport): Promise<void> {
        if (this.acceptingReportId || this.rejectingReportId) return;
        this.rejectingReportId = item.id;

        const payload = {
            tableName: "digisafe",
            id: item.id,
            data: {
                status: DigisafeStatus.Rejected,
                rejectedId: this.loggedInAdminId // Add rejectedId with current userId
            },
            createdBy: this.loggedInAdminId
        };

        try {
            console.log('Executing API call for REJECT:', payload);
            const response = await this.updateService.update(payload);

            // Check if response contains an error code
            if (response && response.responseCode && response.responseCode !== 200) {
                console.error(`Error rejecting report ID ${item.id} (from response):`, response);
                this.toast?.showErrorToast(response.message || `Failed to reject report.`);
                return;
            }

            this.toast?.showErrorToast(`Report ID ${item.id} rejected.`);
            await this.loadDigisafeData();
        } catch (error: any) {
            console.error(`Error rejecting report ID ${item.id}:`, error);
            // Direct error handling for the format { message: "Failed to save", responseCode: 400 }
            if (error && error.responseCode) {
                this.toast?.showErrorToast(error.message || `Failed to reject report.`);
            } else if (error && typeof error === 'object') {
                // Try to extract message from any error object format
                const errorMessage = error.message ||
                                    (error.response && error.response.message) ||
                                    (error.response && error.response.data && error.response.data.message) ||
                                    `Failed to reject report.`;
                this.toast?.showErrorToast(errorMessage);
            } else {
                this.toast?.showErrorToast(`Failed to reject report.`);
            }
        } finally {
            this.rejectingReportId = null;
        }
    }

    // New methods for super admin status changes
    async executeMoveToPending(item: DigisafeReport): Promise<void> {
        if (this.acceptingReportId || this.rejectingReportId) return;
        this.acceptingReportId = item.id; // Reuse the existing loading indicator

        const payload = {
            tableName: "digisafe",
            id: item.id,
            data: {
                status: DigisafeStatus.Pending
            },
            createdBy: this.loggedInAdminId
        };

        try {
            console.log('Executing API call for MOVE TO PENDING:', payload);
            const response = await this.updateService.update(payload);

            // Check if response contains an error code
            if (response && response.responseCode && response.responseCode !== 200) {
                console.error(`Error moving report ID ${item.id} to pending (from response):`, response);
                this.toast?.showErrorToast(response.message || `Failed to change report status.`);
                return;
            }

            this.toast?.showSuccessToast(`Report for ${item.plant?.name || 'plant'} moved to pending state successfully.`);
            await this.loadDigisafeData();
        } catch (error: any) {
            console.error(`Error moving report ID ${item.id} to pending:`, error);
            // Direct error handling for the format { message: "Failed to save", responseCode: 400 }
            if (error && error.responseCode) {
                this.toast?.showErrorToast(error.message || `Failed to change report status.`);
            } else if (error && typeof error === 'object') {
                // Try to extract message from any error object format
                const errorMessage = error.message ||
                                    (error.response && error.response.message) ||
                                    (error.response && error.response.data && error.response.data.message) ||
                                    `Failed to change report status.`;
                this.toast?.showErrorToast(errorMessage);
            } else {
                this.toast?.showErrorToast(`Failed to change report status.`);
            }
        } finally {
            this.acceptingReportId = null;
        }
    }

    async executeMoveToApproved(item: DigisafeReport): Promise<void> {
        if (this.acceptingReportId || this.rejectingReportId) return;
        this.acceptingReportId = item.id;

        const payload = {
            tableName: "digisafe",
            id: item.id,
            data: {
                status: DigisafeStatus.Approved,
                approveId: this.loggedInAdminId
            },
            createdBy: this.loggedInAdminId
        };

        try {
            console.log('Executing API call for MOVE TO APPROVED:', payload);
            const response = await this.updateService.update(payload);

            // Check if response contains an error code
            if (response && response.responseCode && response.responseCode !== 200) {
                console.error(`Error moving report ID ${item.id} to approved (from response):`, response);
                this.toast?.showErrorToast(response.message || `Failed to change report status.`);
                return;
            }

            this.toast?.showSuccessToast(`Report for ${item.plant?.name || 'plant'} moved to approved state successfully.`);
            await this.loadDigisafeData();
        } catch (error: any) {
            console.error(`Error moving report ID ${item.id} to approved:`, error);
            // Direct error handling for the format { message: "Failed to save", responseCode: 400 }
            if (error && error.responseCode) {
                this.toast?.showErrorToast(error.message || `Failed to change report status.`);
            } else if (error && typeof error === 'object') {
                // Try to extract message from any error object format
                const errorMessage = error.message ||
                                    (error.response && error.response.message) ||
                                    (error.response && error.response.data && error.response.data.message) ||
                                    `Failed to change report status.`;
                this.toast?.showErrorToast(errorMessage);
            } else {
                this.toast?.showErrorToast(`Failed to change report status.`);
            }
        } finally {
            this.acceptingReportId = null;
        }
    }

    async executeMoveToRejected(item: DigisafeReport): Promise<void> {
        if (this.acceptingReportId || this.rejectingReportId) return;
        this.rejectingReportId = item.id;

        const payload = {
            tableName: "digisafe",
            id: item.id,
            data: {
                status: DigisafeStatus.Rejected,
                rejectedId: this.loggedInAdminId
            },
            createdBy: this.loggedInAdminId
        };

        try {
            console.log('Executing API call for MOVE TO REJECTED:', payload);
            const response = await this.updateService.update(payload);

            // Check if response contains an error code
            if (response && response.responseCode && response.responseCode !== 200) {
                console.error(`Error moving report ID ${item.id} to rejected (from response):`, response);
                this.toast?.showErrorToast(response.message || `Failed to change report status.`);
                return;
            }

            this.toast?.showSuccessToast(`Report for ${item.plant?.name || 'plant'} moved to rejected state successfully.`);
            await this.loadDigisafeData();
        } catch (error: any) {
            console.error(`Error moving report ID ${item.id} to rejected:`, error);
            // Direct error handling for the format { message: "Failed to save", responseCode: 400 }
            if (error && error.responseCode) {
                this.toast?.showErrorToast(error.message || `Failed to change report status.`);
            } else if (error && typeof error === 'object') {
                // Try to extract message from any error object format
                const errorMessage = error.message ||
                                    (error.response && error.response.message) ||
                                    (error.response && error.response.data && error.response.data.message) ||
                                    `Failed to change report status.`;
                this.toast?.showErrorToast(errorMessage);
            } else {
                this.toast?.showErrorToast(`Failed to change report status.`);
            }
        } finally {
            this.rejectingReportId = null;
        }
    }
    formatDate(dateInput: string | Date | null | undefined, formatType: 'MMM YYYY' | 'from' | 'MMM'): string {
        if (!dateInput) return '';

        const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
        if (isNaN(date?.getTime())) return '';

        switch (formatType) {
            case 'MMM YYYY':
                return date.toLocaleString('en-US', { month: 'short', year: 'numeric' });
            case 'from':
                return date.toISOString().split('T')[0];
            case 'MMM':
                return date.toLocaleString('en-US', { month: 'short' });
            default:
                return '';
        }
    }

    // --- ADDED: Function to populate month/year options ---
    populateAvailableMonthsYears(): void {
        this.availableMonthsYears = [];
        const today = new Date();
        const currentYear = today.getFullYear();
        const currentMonthIndex = today.getMonth(); // 0-indexed

        // Generate options for the current year (from previous month back to January)
        // Exclude the current month when the current year is selected.
        for (let monthIndex = currentMonthIndex - 1; monthIndex >= 0; monthIndex--) {
             const monthDate = new Date(currentYear, monthIndex, 1);
             this.availableMonthsYears.push({
                 year: currentYear,
                 month: monthIndex + 1, // Store as 1-12
                 label: monthDate.toLocaleString('en-US', { month: 'short', year: 'numeric' })
             });
        }

        // Generate options for the previous 5 full years
        for (let yearOffset = 1; yearOffset <= 5; yearOffset++) {
            const targetYear = currentYear - yearOffset;
            for (let monthIndex = 11; monthIndex >= 0; monthIndex--) { // All 12 months, backwards
                 const monthDate = new Date(targetYear, monthIndex, 1);
                 this.availableMonthsYears.push({
                     year: targetYear,
                     month: monthIndex + 1, // Store as 1-12
                     label: monthDate.toLocaleString('en-US', { month: 'short', year: 'numeric' })
                 });
            }
        }

        console.log("Available Month/Year Options (after population):", JSON.stringify(this.availableMonthsYears)); // Log as string for clarity
        // Update unique years list used by create form template
        this.uniqueYearsForCreate = [...new Set(this.availableMonthsYears.map(opt => opt.year))].sort((a, b) => b - a);
    }

    // --- ADDED: Function to calculate total fields ---
    calculateTotals(data: DigisafeFormData | null): void { // Changed param type
        if (!data) return;
        const N = this.safeNumber; // Use helper function

        // Calculate totals based on input fields
        const manHoursTotal = N(data.ManHoursWorkedOwnEmployees) + N(data.ManHoursWorkedConEmployees);
        const facTotal = N(data.FACCompOnsite) + N(data.FACCompOfsite) + N(data.FACconOnsite);
        const mtcTotal = N(data.MTCCompOnsite) + N(data.MTCCompOfsite) + N(data.MTCconOnsite);
        const rltiTotal = N(data.RLTICompOnsite) + N(data.RLTICompOfsite) + N(data.RLTIconOnsite);
        const fatalTotal = N(data.FatalCompOnsite) + N(data.FatalCompOfsite) + N(data.FatalconOnsite);
        const mdlTotal = N(data.MDLEmployees) + N(data.MDLCon);
        const trainingHoursTotal = N(data.TrainingManHoursEmployees) + N(data.TrainingManHoursCon);
        const rwcTotal = N(data.RWCCompOnsite) + N(data.RWCCompOfsite) + N(data.RWCconOnsite);
        const mwdTotal = N(data.MWDEmployee) + N(data.MWDContractorOnsite);
        // Calculate property damage as sum of fire and non-fire incidents
        const propertyDamageTotal = N(data.FireIncident) + N(data.nonFireIncident);

        // Always set calculated fields to a string value, even when the total is 0
        // This ensures that zero values are displayed properly and not as null
        data.ManHoursWorked = String(manHoursTotal);
        data.FAC = String(facTotal);
        data.MTC = String(mtcTotal);
        data.RLTI = String(rltiTotal);
        data.Fatal = String(fatalTotal);
        data.MDL = String(mdlTotal);
        data.TrainingManHours = String(trainingHoursTotal);
        data.RWC = String(rwcTotal);
        data.MWD = String(mwdTotal);
        // Set property damage as calculated field
        data.propertyDamage = String(propertyDamageTotal);

        this.cdr.detectChanges(); // Ensure UI updates
    }

    // --- ADDED: Method to be called from template on change ---
    onBreakdownFieldChange(): void {
        // Calculate totals for the currently active form (create or edit)
        if (this.isCreateModalOpen && this.newDigisafeData) {
            this.calculateTotals(this.newDigisafeData);
        } else if (this.isEditModalOpen && this.editingFormData) {
            this.calculateTotals(this.editingFormData);
        }
    }

    // --- ADDED: Method to handle year selection change in create modal ---
    onYearSelectForCreate(): void {
        console.log("Year selected for create:", this.newDigisafeData.selectedYear, "Type:", typeof this.newDigisafeData.selectedYear); // Log value and type
        // Reset selected month when year changes

        console.log("Filtering months for year:", this.newDigisafeData.selectedYear);
        console.log("Source array for filtering (availableMonthsYears):", JSON.stringify(this.availableMonthsYears)); // Log source array

        // Filter availableMonthsYears based on the selected year
        if (this.newDigisafeData.selectedYear !== null) {
            this.filteredMonthsForCreate = this.availableMonthsYears.filter(
                option => {
                    const selectedYearNumber = Number(this.newDigisafeData.selectedYear); // Convert to number
                    console.log("Comparing option.year:", option.year, "Type:", typeof option.year, "with selectedYear (converted):", selectedYearNumber, "Type:", typeof selectedYearNumber); // Log types during comparison
                    return option.year === selectedYearNumber; // Compare number with number
                }
            );
        } else {
            this.filteredMonthsForCreate = []; // Clear months if no year is selected
        }

        // Reset selected month *after* filtering and *before* change detection
        this.newDigisafeData.selectedMonth = null;

        console.log("Filtered Month/Year Options (after filtering):", JSON.stringify(this.filteredMonthsForCreate)); // Log result as string

        // Trigger change detection to update the month dropdown options
        this.cdr.detectChanges();
    }

} // End of component class