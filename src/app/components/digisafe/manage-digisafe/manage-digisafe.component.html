<!-- manage-digisafe.component.html -->

<!-- Tab Navigation -->
<app-toast-message></app-toast-message>
<app-tab [tabs]="tabs" [selectedTabIndex]="selectedTabIndex" (tabSelected)="onTabSelected($event)">
</app-tab>

<!-- Pending List (Tab 0) -->
<div *ngIf="selectedTabIndex == 0" class="card custom-card" id="pending-list">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h6 class="mb-0">DigiSafe Report</h6>
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <!-- Add Report Button -->
                <button class="btn-sm adani-btn me-2" (click)="openCreateModal()" title="Add New DigiSafe Report">
                    <i class="bi bi-plus-circle"></i> Add Report
                </button>
                <!-- Download Dropdown -->
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
                        id="downloadDigiSafeExcelDropdownPending" ngbDropdownToggle
                        [disabled]="isDownloadingExcel || isLoading">
                        <span *ngIf="!isDownloadingExcel">
                            <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel">
                            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '')
                            }}...
                        </span>
                    </button>
                    <ul ngbDropdownMenu aria-labelledby="downloadDigiSafeExcelDropdownPending">
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || isLoading || (getCurrentListData()?.length ?? 0) === 0">
                                <i class="bi bi-download me-1"></i> Download Current Page ({{
                                getCurrentListData()?.length ?? 0 }})
                            </button>
                        </li>
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || isLoading || totalItems === 0">
                                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                            </button>
                        </li>
                    </ul>
                </div>
                <!-- Filter Button -->
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="Filter" style="width: 35px; cursor: pointer;" title="Filter Reports"/>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered custom-table data-detail-table">
                <thead class="table-header">
                    <tr>
                        <th scope="col" class="text-center">Plant Name</th>
                        <th scope="col" class="text-center" style="min-width: 450px;">Safety Report Details</th>
                        <th scope="col" class="text-center">Submitted By / Actions</th>
                        <th scope="col" class="text-center">Created Date</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Loading State -->
                    <tr *ngIf="isLoading">
                        <td colspan="4" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Loading pending reports...
                        </td>
                    </tr>
                    <!-- No Data State -->
                    <tr *ngIf="!isLoading && (!pendingList || pendingList.length === 0)">
                        <td colspan="4" class="text-center p-4 text-muted">
                            No pending reports found matching the current filters.
                        </td>
                    </tr>
                    <!-- Data Rows -->
                    <tr *ngFor="let item of pendingList">
                        <td class="text-center align-middle">
                            <span>{{ item.plant?.name || 'N/A' }}</span>
                        </td>
                        <!-- Safety Details Cell -->
                        <td class="safety-report-details-cell">
                            <div class="safety-report-details-content">
                                <div class="column-layout">
                                    <div class="column"> <!-- Left Column -->
                                        <div class="detail-row"><span class="detail-label">Accident Free Period
                                                (Current):</span><span>{{ item.AfpCurrent }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Accident Free Period
                                                (Best):</span><span>{{ item.AfpBest }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Man Hours
                                                Worked:</span><span>{{ item.ManHoursWorked }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Kilometres
                                                Covered:</span><span>{{ item.KmCovered }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Occupational Illness
                                                (OI):</span><span>{{ item.OI }}</span></div>
                                        <div class="detail-row"><span class="detail-label">First Aid Case
                                                (FAC):</span><span>{{ item.FAC }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Medical Treatment Case
                                                (MTC):</span><span>{{ item.MTC }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Reportable Lost Time Injury
                                                (R-LTI):</span><span>{{ item.RLTI }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal:</span><span>{{
                                                item.Fatal }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Dangerous Occurrence
                                                (DO):</span><span>{{ item.DangerOccurence }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fire Incident:</span><span>{{
                                                item.FireIncident }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Vehicle related
                                                Accident:</span><span>{{ item.VRA }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Property Damage Incident
                                                Nos.:</span><span>{{ item.PDI }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Leak-Spill
                                                Incident:</span><span>{{ item.LSI }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Accident Cost INR (Divided by
                                                1000):</span><span>{{ item.AccidentCost }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Man Days Lost
                                                (MDL):</span><span>{{ item.MDL }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Near miss Incident
                                                :</span><span>{{ item.NearMissIncident }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Induction
                                                (Participants):</span><span>{{ item.SafetyInduction }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Tool Box Talk (No of
                                                batches.):</span><span>{{ item.TBTBatches }}</span></div>
                                        <div class="detail-row"><span class="detail-label">TBT- Participant
                                                Nos.:</span><span>{{ item.TBTno }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Training Program Plan
                                                (Batches):</span><span>{{ item.STP_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Training Program - MTD
                                                (Batches):</span><span>{{ item.STP_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Employee Trained - Plan
                                                (Nos):</span><span>{{ item.ET_Plan }}</span></div>
                                        <!-- ADDED Fields (Left Column) -->
                                        <div class="detail-row"><span class="detail-label">Man Hours (Own
                                                Emp):</span><span>{{ item.ManHoursWorkedOwnEmployees ?? 'N/A' }}</span>
                                        </div>
                                        <div class="detail-row"><span class="detail-label">FAC (Emp
                                                Onsite):</span><span>{{ item.FACCompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">FAC (Emp
                                                Offsite):</span><span>{{ item.FACCompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Emp
                                                Onsite):</span><span>{{ item.MTCCompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Emp
                                                Offsite):</span><span>{{ item.MTCCompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Visitor):</span><span>{{
                                                item.MTCthirdPartyVisitor ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI (Emp
                                                Onsite):</span><span>{{ item.RLTICompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI (Emp
                                                Offsite):</span><span>{{ item.RLTICompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI
                                                (Visitor):</span><span>{{ item.RLTIthirdPartyVisitor ?? 'N/A' }}</span>
                                        </div>
                                        <div class="detail-row"><span class="detail-label">Fatal (Emp
                                                Onsite):</span><span>{{ item.FatalCompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal (Emp
                                                Offsite):</span><span>{{ item.FatalCompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal
                                                (Visitor):</span><span>{{ item.FatalthirdPartyVisitor ?? 'N/A' }}</span>
                                        </div>
                                        <div class="detail-row"><span class="detail-label">Property
                                                Damage:</span><span>{{ item.propertyDamage ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MDL
                                                (Employees):</span><span>{{ item.MDLEmployees ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Training Hours
                                                (Emp):</span><span>{{ item.TrainingManHoursEmployees ?? 'N/A' }}</span>
                                        </div>
                                        <div class="detail-row"><span class="detail-label">RWC (Total):</span><span>{{
                                                item.RWC ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">RWC (Emp
                                                Onsite):</span><span>{{ item.RWCCompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">RWC (Emp
                                                Offsite):</span><span>{{ item.RWCCompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MWD (Total):</span><span>{{
                                                item.MWD ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MWD
                                                (Employee):</span><span>{{ item.MWDEmployee ?? 'N/A' }}</span></div>
                                    </div>
                                    <div class="vl"></div>
                                    <div class="column"> <!-- Right Column -->
                                        <div class="detail-row"><span class="detail-label">Employee Trained - MTD
                                                (Nos):</span><span>{{ item.ET_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Training Man-Hours
                                                (MTD):</span><span>{{ item.TrainingManHours }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Committee Meetings
                                                (Plan):</span><span>{{ item.SCMPlan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Committee Meetings
                                                (MTD):</span><span>{{ item.SCM_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Permit To Work Audit
                                                (Plan):</span><span>{{ item.PWAPlan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Permit To Work Audit (MTD)
                                                :</span><span>{{ item.PWA_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Self Assessment
                                                (Plan):</span><span>{{ item.SA_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Self Assessment
                                                (MTD):</span><span>{{ item.SA_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Inspection
                                                (Plan):</span><span>{{ item.SI_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Inspection
                                                (MTD):</span><span>{{ item.SI_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety-Item / Equipment
                                                Inspection (Plan):</span><span>{{ item.EI_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety-Item / Equipment
                                                Inspection (MTD):</span><span>{{ item.EI_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Promotional Campaign
                                                (Plan):</span><span>{{ item.SPC_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Promotional Campaign
                                                (MTD):</span><span>{{ item.SPC_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Emergency Mock Drill
                                                (Plan):</span><span>{{ item.EMD_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Emergency Mock Drill
                                                (MTD):</span><span>{{ item.EMD_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Industrial Hygiene Monitoring
                                                (Plan):</span><span>{{ item.IHM_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Industrial Hygiene Monitoring
                                                (MTD) :</span><span>{{ item.IHM_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Notification
                                                Raised:</span><span>{{ item.SnRaised }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Notification
                                                Closed:</span><span>{{ item.SnClosed }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Notification
                                                Open:</span><span>{{ item.SnOpen }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Notification Open > 90
                                                Days:</span><span>{{ item.SnOpen90days }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Walkthrough by Senior
                                                leadership Team:</span><span>{{ item.SafetyWalkthrough }}</span></div>
                                        <!-- ADDED Fields (Right Column) -->
                                        <div class="detail-row"><span class="detail-label">Man Hours
                                                (Contractor):</span><span>{{ item.ManHoursWorkedConEmployees ?? 'N/A'
                                                }}</span></div>
                                        <div class="detail-row"><span class="detail-label">FAC (Con
                                                Onsite):</span><span>{{ item.FACconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">FAC (Con
                                                Offsite):</span><span>{{ item.FACconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Con
                                                Onsite):</span><span>{{ item.MTCconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Con
                                                Offsite):</span><span>{{ item.MTCconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI (Con
                                                Onsite):</span><span>{{ item.RLTIconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI (Con
                                                Offsite):</span><span>{{ item.RLTIconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal (Con
                                                Onsite):</span><span>{{ item.FatalconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal (Con
                                                Offsite):</span><span>{{ item.FatalconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Non-Fire
                                                Incident:</span><span>{{ item.nonFireIncident ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MDL
                                                (Contractor):</span><span>{{ item.MDLCon ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Training Hours
                                                (Con):</span><span>{{ item.TrainingManHoursCon ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">RWC (Con
                                                Onsite):</span><span>{{ item.RWCconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">RWC (Con
                                                Offsite):</span><span>{{ item.RWCconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MWD (Con
                                                Onsite):</span><span>{{ item.MWDContractorOnsite ?? 'N/A' }}</span>
                                        </div>
                                        <!-- Month and Year Display -->
                                        <div class="detail-row"><span class="detail-label">Month and
                                                Year:</span><span>{{ formatDate(item.createdTimestamp, 'MMM YYYY')
                                                }}</span></div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="updated-by-cell text-center align-middle">
                            <!-- Submitted By Details -->
                            <div *ngIf="item.updated != null" class="updated-by-details mb-2 text-start small">
                                <div class="detail-row"><span class="detail-label">Name:</span><span>{{
                                        item.updated?.firstName }} {{ item.updated?.lastName }}</span></div>
                                <div class="detail-row"><span class="detail-label">Contact:</span><span>{{
                                        item.updated?.contactNumber || 'N/A' }}</span></div>
                                <div class="detail-row"><span class="detail-label">Email:</span><span>{{
                                        item.updated?.email || 'N/A' }}</span></div>
                            </div>
                            <span *ngIf="!item.updated" class="text-muted d-block mb-2 small">N/A</span>
                            <!-- Action Buttons -->
                            <hr *ngIf="item.updated != null">
                            <!-- Action Buttons -->
                            <!-- Accept/Reject buttons only for Super Admin or Plant Admin with department ID 32 -->
                            <div class="d-flex justify-content-center gap-2 mt-2 flex-wrap"
                                *ngIf="currentUserRole === componentRoles.SUPER_ADMIN || (currentUserRole === componentRoles.PLANT_ADMIN && loggedInUserDepartmentId === 32)">
                                <button class="btn btn-success btn-sm flex-fill" style="min-width: 40px;"
                                    title="Accept Report" (click)="openConfirmationModal(item, 'accept')"
                                    [disabled]="acceptingReportId === item.id || rejectingReportId === item.id || currentUserRole === ''">
                                    <span *ngIf="acceptingReportId !== item.id"><i
                                            class="bi bi-check-circle"></i></span>
                                    <span *ngIf="acceptingReportId === item.id" class="spinner-border spinner-border-sm"
                                        role="status" aria-hidden="true"></span>
                                </button>
                                <button class="btn btn-danger btn-sm flex-fill" style="min-width: 40px;"
                                    title="Reject Report" (click)="openConfirmationModal(item, 'reject')"
                                    [disabled]="acceptingReportId === item.id || rejectingReportId === item.id || currentUserRole === ''">
                                    <span *ngIf="rejectingReportId !== item.id"><i class="bi bi-x-circle"></i></span>
                                    <span *ngIf="rejectingReportId === item.id" class="spinner-border spinner-border-sm"
                                        role="status" aria-hidden="true"></span>
                                </button>
                                <button class="btn btn-warning btn-sm flex-fill" style="min-width: 40px;"
                                    title="Edit Report" (click)="openEditModal(item)"
                                    [disabled]="acceptingReportId === item.id || rejectingReportId === item.id || currentUserRole === ''">
                                    <i class="bi bi-pencil-square"></i>
                                </button>
                            </div>

                            <!-- Edit button for Plant Admin without department ID 32 -->
                            <div class="d-flex justify-content-center gap-2 mt-2 flex-wrap"
                                *ngIf="currentUserRole === componentRoles.PLANT_ADMIN && loggedInUserDepartmentId !== 32">
                                <button class="btn btn-warning btn-sm flex-fill" style="min-width: 40px;"
                                    title="Edit Report" (click)="openEditModal(item)"
                                    [disabled]="acceptingReportId === item.id || rejectingReportId === item.id || currentUserRole === ''">
                                    <i class="bi bi-pencil-square"></i>
                                </button>
                            </div>
                        </td>
                        <td class="text-center align-middle"> <!-- Created Date -->
                            <span style="color: #dc3545; font-size: 0.9em;">
                                {{ item.updatedTimestamp | date:'dd MMM yyyy' }} <br>
                                <small>{{ item.updatedTimestamp | date:'h:mm a' }}</small>
                            </span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Approved List (Tab 1) -->
<div *ngIf="selectedTabIndex == 1" class="card custom-card" id="approved-list">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h6 class="mb-0">Approved DigiSafe Reports</h6>
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <!-- Download Dropdown -->
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
                        id="downloadDigiSafeExcelDropdownApproved" ngbDropdownToggle
                        [disabled]="isDownloadingExcel || isLoading">
                        <span *ngIf="!isDownloadingExcel"><i class="bi bi-file-earmark-excel me-1"></i> Download
                            Excel</span>
                        <span *ngIf="isDownloadingExcel"><span class="spinner-border spinner-border-sm me-1"></span>
                            Downloading...</span>
                    </button>
                    <ul ngbDropdownMenu aria-labelledby="downloadDigiSafeExcelDropdownApproved">
                        <li><button ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || isLoading || (getCurrentListData()?.length ?? 0) === 0"><i
                                    class="bi bi-download me-1"></i> Current Page ({{ getCurrentListData()?.length ?? 0
                                }})</button></li>
                        <li><button ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || isLoading || totalItems === 0"><i
                                    class="bi bi-cloud-download me-1"></i> All Filtered ({{ totalItems }})</button></li>
                    </ul>
                </div>
                <!-- Filter Button -->
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="Filter" style="width: 35px; cursor: pointer;" title="Filter Reports"/>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered custom-table data-detail-table">
                <thead class="table-header">
                    <tr>
                        <th scope="col" class="text-center">Plant Name</th>
                        <th scope="col" class="text-center" style="min-width: 450px;">Safety Report Details</th>
                        <th scope="col" class="text-center">Approved By</th>
                        <th scope="col" class="text-center">Approved Date</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngIf="isLoading">
                        <td colspan="4" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Loading approved reports...
                        </td>
                    </tr>
                    <tr *ngIf="!isLoading && (!approvedList || approvedList.length === 0)">
                        <td colspan="4" class="text-center p-4 text-muted">No approved reports found matching the
                            current filters.</td>
                    </tr>
                    <tr *ngFor="let item of approvedList">
                        <td class="text-center align-middle"><span>{{ item.plant?.name || 'N/A'}}</span></td>
                        <!-- Safety Details Cell -->
                        <td class="safety-report-details-cell">
                            <div class="safety-report-details-content">
                                <div class="column-layout">
                                    <div class="column"> <!-- Left Column -->
                                        <div class="detail-row"><span class="detail-label">Accident Free Period
                                                (Current):</span><span>{{ item.AfpCurrent }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Accident Free Period
                                                (Best):</span><span>{{ item.AfpBest }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Man Hours
                                                Worked:</span><span>{{ item.ManHoursWorked }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Kilometres
                                                Covered:</span><span>{{ item.KmCovered }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Occupational Illness
                                                (OI):</span><span>{{ item.OI }}</span></div>
                                        <div class="detail-row"><span class="detail-label">First Aid Case
                                                (FAC):</span><span>{{ item.FAC }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Medical Treatment Case
                                                (MTC):</span><span>{{ item.MTC }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Reportable Lost Time Injury
                                                (R-LTI):</span><span>{{ item.RLTI }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal:</span><span>{{
                                                item.Fatal }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Dangerous Occurrence
                                                (DO):</span><span>{{ item.DangerOccurence }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fire Incident:</span><span>{{
                                                item.FireIncident }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Vehicle related
                                                Accident:</span><span>{{ item.VRA }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Property Damage Incident
                                                Nos.:</span><span>{{ item.PDI }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Leak-Spill
                                                Incident:</span><span>{{ item.LSI }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Accident Cost INR (Divided by
                                                1000):</span><span>{{ item.AccidentCost }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Man Days Lost
                                                (MDL):</span><span>{{ item.MDL }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Near miss Incident
                                                :</span><span>{{ item.NearMissIncident }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Induction
                                                (Participants):</span><span>{{ item.SafetyInduction }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Tool Box Talk (No of
                                                batches.):</span><span>{{ item.TBTBatches }}</span></div>
                                        <div class="detail-row"><span class="detail-label">TBT- Participant
                                                Nos.:</span><span>{{ item.TBTno }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Training Program Plan
                                                (Batches):</span><span>{{ item.STP_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Training Program - MTD
                                                (Batches):</span><span>{{ item.STP_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Employee Trained - Plan
                                                (Nos):</span><span>{{ item.ET_Plan }}</span></div>
                                        <!-- ADDED Fields (Left Column) -->
                                        <div class="detail-row"><span class="detail-label">Man Hours (Own
                                                Emp):</span><span>{{ item.ManHoursWorkedOwnEmployees ?? 'N/A' }}</span>
                                        </div>
                                        <div class="detail-row"><span class="detail-label">FAC (Emp
                                                Onsite):</span><span>{{ item.FACCompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">FAC (Emp
                                                Offsite):</span><span>{{ item.FACCompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Emp
                                                Onsite):</span><span>{{ item.MTCCompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Emp
                                                Offsite):</span><span>{{ item.MTCCompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Visitor):</span><span>{{
                                                item.MTCthirdPartyVisitor ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI (Emp
                                                Onsite):</span><span>{{ item.RLTICompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI (Emp
                                                Offsite):</span><span>{{ item.RLTICompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI
                                                (Visitor):</span><span>{{ item.RLTIthirdPartyVisitor ?? 'N/A' }}</span>
                                        </div>
                                        <div class="detail-row"><span class="detail-label">Fatal (Emp
                                                Onsite):</span><span>{{ item.FatalCompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal (Emp
                                                Offsite):</span><span>{{ item.FatalCompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal
                                                (Visitor):</span><span>{{ item.FatalthirdPartyVisitor ?? 'N/A' }}</span>
                                        </div>
                                        <div class="detail-row"><span class="detail-label">Property
                                                Damage:</span><span>{{ item.propertyDamage ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MDL
                                                (Employees):</span><span>{{ item.MDLEmployees ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Training Hours
                                                (Emp):</span><span>{{ item.TrainingManHoursEmployees ?? 'N/A' }}</span>
                                        </div>
                                        <div class="detail-row"><span class="detail-label">RWC (Total):</span><span>{{
                                                item.RWC ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">RWC (Emp
                                                Onsite):</span><span>{{ item.RWCCompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">RWC (Emp
                                                Offsite):</span><span>{{ item.RWCCompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MWD (Total):</span><span>{{
                                                item.MWD ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MWD
                                                (Employee):</span><span>{{ item.MWDEmployee ?? 'N/A' }}</span></div>
                                    </div>
                                    <div class="vl"></div>
                                    <div class="column"> <!-- Right Column -->
                                        <div class="detail-row"><span class="detail-label">Employee Trained - MTD
                                                (Nos):</span><span>{{ item.ET_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Training Man-Hours
                                                (MTD):</span><span>{{ item.TrainingManHours }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Committee Meetings
                                                (Plan):</span><span>{{ item.SCMPlan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Committee Meetings
                                                (MTD):</span><span>{{ item.SCM_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Permit To Work Audit
                                                (Plan):</span><span>{{ item.PWAPlan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Permit To Work Audit (MTD)
                                                :</span><span>{{ item.PWA_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Self Assessment
                                                (Plan):</span><span>{{ item.SA_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Self Assessment
                                                (MTD):</span><span>{{ item.SA_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Inspection
                                                (Plan):</span><span>{{ item.SI_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Inspection
                                                (MTD):</span><span>{{ item.SI_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety-Item / Equipment
                                                Inspection (Plan):</span><span>{{ item.EI_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety-Item / Equipment
                                                Inspection (MTD):</span><span>{{ item.EI_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Promotional Campaign
                                                (Plan):</span><span>{{ item.SPC_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Promotional Campaign
                                                (MTD):</span><span>{{ item.SPC_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Emergency Mock Drill
                                                (Plan):</span><span>{{ item.EMD_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Emergency Mock Drill
                                                (MTD):</span><span>{{ item.EMD_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Industrial Hygiene Monitoring
                                                (Plan):</span><span>{{ item.IHM_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Industrial Hygiene Monitoring
                                                (MTD) :</span><span>{{ item.IHM_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Notification
                                                Raised:</span><span>{{ item.SnRaised }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Notification
                                                Closed:</span><span>{{ item.SnClosed }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Notification
                                                Open:</span><span>{{ item.SnOpen }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Notification Open > 90
                                                Days:</span><span>{{ item.SnOpen90days }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Walkthrough by Senior
                                                leadership Team:</span><span>{{ item.SafetyWalkthrough }}</span></div>
                                        <!-- ADDED Fields (Right Column) -->
                                        <div class="detail-row"><span class="detail-label">Man Hours
                                                (Contractor):</span><span>{{ item.ManHoursWorkedConEmployees ?? 'N/A'
                                                }}</span></div>
                                        <div class="detail-row"><span class="detail-label">FAC (Con
                                                Onsite):</span><span>{{ item.FACconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">FAC (Con
                                                Offsite):</span><span>{{ item.FACconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Con
                                                Onsite):</span><span>{{ item.MTCconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Con
                                                Offsite):</span><span>{{ item.MTCconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI (Con
                                                Onsite):</span><span>{{ item.RLTIconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI (Con
                                                Offsite):</span><span>{{ item.RLTIconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal (Con
                                                Onsite):</span><span>{{ item.FatalconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal (Con
                                                Offsite):</span><span>{{ item.FatalconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Non-Fire
                                                Incident:</span><span>{{ item.nonFireIncident ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MDL
                                                (Contractor):</span><span>{{ item.MDLCon ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Training Hours
                                                (Con):</span><span>{{ item.TrainingManHoursCon ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">RWC (Con
                                                Onsite):</span><span>{{ item.RWCconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">RWC (Con
                                                Offsite):</span><span>{{ item.RWCconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MWD (Con
                                                Onsite):</span><span>{{ item.MWDContractorOnsite ?? 'N/A' }}</span>
                                        </div>
                                        <!-- Month and Year Display -->
                                        <div class="detail-row"><span class="detail-label">Month and
                                                Year:</span><span>{{ formatDate(item.createdTimestamp, 'MMM YYYY')
                                                }}</span></div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="approved-by-cell text-center align-middle"> <!-- Approved By -->
                            <div *ngIf="item.approve != null" class="approved-by-details text-start small">
                                <div class="detail-row"><span class="detail-label">Name:</span><span>{{
                                        item.approve?.firstName }} {{ item.approve?.lastName }}</span></div>
                                <div class="detail-row"><span class="detail-label">Contact:</span><span>{{
                                        item.approve?.contactNumber || 'N/A' }}</span></div>
                                <div class="detail-row"><span class="detail-label">Email:</span><span>{{
                                        item.approve?.email || 'N/A' }}</span></div>
                            </div>
                            <span *ngIf="!item.approve" class="text-muted small">N/A</span>

                            <!-- Action Buttons for Super Admin -->
                            <hr *ngIf="currentUserRole === componentRoles.SUPER_ADMIN">
                            <div class="d-flex justify-content-center gap-2 mt-2 flex-wrap"
                                *ngIf="currentUserRole === componentRoles.SUPER_ADMIN">
                                <button type="button" class="btn btn-warning btn-sm flex-fill" style="min-width: 40px;"
                                    title="Edit Report" (click)="openEditModal(item)">
                                    <i class="bi bi-pencil-square"></i>
                                </button>
                                <button type="button" class="btn btn-danger btn-sm flex-fill" style="min-width: 40px;"
                                    title="Move to Rejected" (click)="openConfirmationModal(item, 'reject-approved')"
                                    [disabled]="acceptingReportId === item.id || rejectingReportId === item.id">
                                    <span *ngIf="rejectingReportId !== item.id"><i class="bi bi-x-circle"></i></span>
                                    <span *ngIf="rejectingReportId === item.id" class="spinner-border spinner-border-sm"
                                        role="status" aria-hidden="true"></span>
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm flex-fill" style="min-width: 40px;"
                                    title="Move to Pending" (click)="openConfirmationModal(item, 'pending')"
                                    [disabled]="acceptingReportId === item.id || rejectingReportId === item.id">
                                    <span *ngIf="acceptingReportId !== item.id"><i class="bi bi-arrow-counterclockwise"></i></span>
                                    <span *ngIf="acceptingReportId === item.id" class="spinner-border spinner-border-sm"
                                        role="status" aria-hidden="true"></span>
                                </button>
                            </div>
                        </td>
                        <td class="text-center align-middle"> <!-- Approved Date -->
                            <span *ngIf="item.approvedTimestamp" style="color: #198754; font-size: 0.9em;">
                                {{ item.approvedTimestamp | date:'dd MMM yyyy' }} <br>
                                <small>{{ item.approvedTimestamp | date:'h:mm a' }}</small>
                            </span>
                            <span *ngIf="!item.approvedTimestamp" style="color: #198754; font-size: 0.9em;">
                                {{ item.updatedTimestamp | date:'dd MMM yyyy' }} <br>
                                <small>{{ item.updatedTimestamp | date:'h:mm a' }}</small>
                            </span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Rejected List (Tab 2) -->
<div *ngIf="selectedTabIndex == 2" class="card custom-card" id="rejected-list">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h6 class="mb-0">Rejected DigiSafe Reports</h6>
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <!-- Download Dropdown -->
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
                        id="downloadDigiSafeExcelDropdownRejected" ngbDropdownToggle
                        [disabled]="isDownloadingExcel || isLoading">
                        <span *ngIf="!isDownloadingExcel"><i class="bi bi-file-earmark-excel me-1"></i> Download
                            Excel</span>
                        <span *ngIf="isDownloadingExcel"><span class="spinner-border spinner-border-sm me-1"></span>
                            Downloading...</span>
                    </button>
                    <ul ngbDropdownMenu aria-labelledby="downloadDigiSafeExcelDropdownRejected">
                        <li><button ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || isLoading || (getCurrentListData()?.length ?? 0) === 0"><i
                                    class="bi bi-download me-1"></i> Current Page ({{ getCurrentListData()?.length ?? 0
                                }})</button></li>
                        <li><button ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || isLoading || totalItems === 0"><i
                                    class="bi bi-cloud-download me-1"></i> All Filtered ({{ totalItems }})</button></li>
                    </ul>
                </div>
                <!-- Filter Button -->
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="Filter" style="width: 35px; cursor: pointer;" title="Filter Reports"/>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered custom-table data-detail-table">
                <thead class="table-header">
                    <tr>
                        <th scope="col" class="text-center">Plant Name</th>
                        <th scope="col" class="text-center" style="min-width: 450px;">Safety Report Details</th>
                        <th scope="col" class="text-center">Rejected By / Reason</th>
                        <th scope="col" class="text-center">Rejected Date</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngIf="isLoading">
                        <td colspan="4" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Loading rejected reports...
                        </td>
                    </tr>
                    <tr *ngIf="!isLoading && (!rejectedList || rejectedList.length === 0)">
                        <td colspan="4" class="text-center p-4 text-muted">No rejected reports found matching the
                            current filters.</td>
                    </tr>
                    <tr *ngFor="let item of rejectedList">
                        <td class="text-center align-middle"><span>{{ item.plant?.name || 'N/A' }}</span></td>
                        <!-- Safety Details Cell -->
                        <td class="safety-report-details-cell">
                            <div class="safety-report-details-content">
                                <div class="column-layout">
                                    <div class="column"> <!-- Left Column -->
                                        <div class="detail-row"><span class="detail-label">Accident Free Period
                                                (Current):</span><span>{{ item.AfpCurrent }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Accident Free Period
                                                (Best):</span><span>{{ item.AfpBest }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Man Hours
                                                Worked:</span><span>{{ item.ManHoursWorked }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Kilometres
                                                Covered:</span><span>{{ item.KmCovered }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Occupational Illness
                                                (OI):</span><span>{{ item.OI }}</span></div>
                                        <div class="detail-row"><span class="detail-label">First Aid Case
                                                (FAC):</span><span>{{ item.FAC }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Medical Treatment Case
                                                (MTC):</span><span>{{ item.MTC }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Reportable Lost Time Injury
                                                (R-LTI):</span><span>{{ item.RLTI }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal:</span><span>{{
                                                item.Fatal }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Dangerous Occurrence
                                                (DO):</span><span>{{ item.DangerOccurence }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fire Incident:</span><span>{{
                                                item.FireIncident }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Vehicle related
                                                Accident:</span><span>{{ item.VRA }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Property Damage Incident
                                                Nos.:</span><span>{{ item.PDI }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Leak-Spill
                                                Incident:</span><span>{{ item.LSI }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Accident Cost INR (Divided by
                                                1000):</span><span>{{ item.AccidentCost }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Man Days Lost
                                                (MDL):</span><span>{{ item.MDL }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Near miss Incident
                                                :</span><span>{{ item.NearMissIncident }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Induction
                                                (Participants):</span><span>{{ item.SafetyInduction }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Tool Box Talk (No of
                                                batches.):</span><span>{{ item.TBTBatches }}</span></div>
                                        <div class="detail-row"><span class="detail-label">TBT- Participant
                                                Nos.:</span><span>{{ item.TBTno }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Training Program Plan
                                                (Batches):</span><span>{{ item.STP_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Training Program - MTD
                                                (Batches):</span><span>{{ item.STP_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Employee Trained - Plan
                                                (Nos):</span><span>{{ item.ET_Plan }}</span></div>
                                        <!-- ADDED Fields (Left Column) -->
                                        <div class="detail-row"><span class="detail-label">Man Hours (Own
                                                Emp):</span><span>{{ item.ManHoursWorkedOwnEmployees ?? 'N/A' }}</span>
                                        </div>
                                        <div class="detail-row"><span class="detail-label">FAC (Emp
                                                Onsite):</span><span>{{ item.FACCompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">FAC (Emp
                                                Offsite):</span><span>{{ item.FACCompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Emp
                                                Onsite):</span><span>{{ item.MTCCompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Emp
                                                Offsite):</span><span>{{ item.MTCCompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Visitor):</span><span>{{
                                                item.MTCthirdPartyVisitor ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI (Emp
                                                Onsite):</span><span>{{ item.RLTICompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI (Emp
                                                Offsite):</span><span>{{ item.RLTICompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI
                                                (Visitor):</span><span>{{ item.RLTIthirdPartyVisitor ?? 'N/A' }}</span>
                                        </div>
                                        <div class="detail-row"><span class="detail-label">Fatal (Emp
                                                Onsite):</span><span>{{ item.FatalCompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal (Emp
                                                Offsite):</span><span>{{ item.FatalCompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal
                                                (Visitor):</span><span>{{ item.FatalthirdPartyVisitor ?? 'N/A' }}</span>
                                        </div>
                                        <div class="detail-row"><span class="detail-label">Property
                                                Damage:</span><span>{{ item.propertyDamage ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MDL
                                                (Employees):</span><span>{{ item.MDLEmployees ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Training Hours
                                                (Emp):</span><span>{{ item.TrainingManHoursEmployees ?? 'N/A' }}</span>
                                        </div>
                                        <div class="detail-row"><span class="detail-label">RWC (Total):</span><span>{{
                                                item.RWC ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">RWC (Emp
                                                Onsite):</span><span>{{ item.RWCCompOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">RWC (Emp
                                                Offsite):</span><span>{{ item.RWCCompOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MWD (Total):</span><span>{{
                                                item.MWD ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MWD
                                                (Employee):</span><span>{{ item.MWDEmployee ?? 'N/A' }}</span></div>
                                    </div>
                                    <div class="vl"></div>
                                    <div class="column"> <!-- Right Column -->
                                        <div class="detail-row"><span class="detail-label">Employee Trained - MTD
                                                (Nos):</span><span>{{ item.ET_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Training Man-Hours
                                                (MTD):</span><span>{{ item.TrainingManHours }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Committee Meetings
                                                (Plan):</span><span>{{ item.SCMPlan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Committee Meetings
                                                (MTD):</span><span>{{ item.SCM_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Permit To Work Audit
                                                (Plan):</span><span>{{ item.PWAPlan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Permit To Work Audit (MTD)
                                                :</span><span>{{ item.PWA_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Self Assessment
                                                (Plan):</span><span>{{ item.SA_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Self Assessment
                                                (MTD):</span><span>{{ item.SA_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Inspection
                                                (Plan):</span><span>{{ item.SI_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Inspection
                                                (MTD):</span><span>{{ item.SI_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety-Item / Equipment
                                                Inspection (Plan):</span><span>{{ item.EI_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety-Item / Equipment
                                                Inspection (MTD):</span><span>{{ item.EI_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Promotional Campaign
                                                (Plan):</span><span>{{ item.SPC_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Promotional Campaign
                                                (MTD):</span><span>{{ item.SPC_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Emergency Mock Drill
                                                (Plan):</span><span>{{ item.EMD_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Emergency Mock Drill
                                                (MTD):</span><span>{{ item.EMD_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Industrial Hygiene Monitoring
                                                (Plan):</span><span>{{ item.IHM_Plan }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Industrial Hygiene Monitoring
                                                (MTD) :</span><span>{{ item.IHM_MTD }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Notification
                                                Raised:</span><span>{{ item.SnRaised }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Notification
                                                Closed:</span><span>{{ item.SnClosed }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Notification
                                                Open:</span><span>{{ item.SnOpen }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Notification Open > 90
                                                Days:</span><span>{{ item.SnOpen90days }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Safety Walkthrough by Senior
                                                leadership Team:</span><span>{{ item.SafetyWalkthrough }}</span></div>
                                        <!-- ADDED Fields (Right Column) -->
                                        <div class="detail-row"><span class="detail-label">Man Hours
                                                (Contractor):</span><span>{{ item.ManHoursWorkedConEmployees ?? 'N/A'
                                                }}</span></div>
                                        <div class="detail-row"><span class="detail-label">FAC (Con
                                                Onsite):</span><span>{{ item.FACconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">FAC (Con
                                                Offsite):</span><span>{{ item.FACconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Con
                                                Onsite):</span><span>{{ item.MTCconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MTC (Con
                                                Offsite):</span><span>{{ item.MTCconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI (Con
                                                Onsite):</span><span>{{ item.RLTIconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">R-LTI (Con
                                                Offsite):</span><span>{{ item.RLTIconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal (Con
                                                Onsite):</span><span>{{ item.FatalconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Fatal (Con
                                                Offsite):</span><span>{{ item.FatalconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Non-Fire
                                                Incident:</span><span>{{ item.nonFireIncident ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MDL
                                                (Contractor):</span><span>{{ item.MDLCon ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">Training Hours
                                                (Con):</span><span>{{ item.TrainingManHoursCon ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">RWC (Con
                                                Onsite):</span><span>{{ item.RWCconOnsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">RWC (Con
                                                Offsite):</span><span>{{ item.RWCconOfsite ?? 'N/A' }}</span></div>
                                        <div class="detail-row"><span class="detail-label">MWD (Con
                                                Onsite):</span><span>{{ item.MWDContractorOnsite ?? 'N/A' }}</span>
                                        </div>
                                        <!-- Month and Year Display -->
                                        <div class="detail-row"><span class="detail-label">Month and
                                                Year:</span><span>{{ formatDate(item.createdTimestamp, 'MMM YYYY')
                                                }}</span></div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="rejected-by-cell text-center align-middle"> <!-- Rejected By / Reason -->
                            <div *ngIf="item.rejected != null" class="rejected-by-details text-start small">
                                <div class="detail-row"><span class="detail-label">Name:</span><span>{{
                                        item.rejected?.firstName }} {{ item.rejected?.lastName }}</span></div>
                                <div class="detail-row"><span class="detail-label">Contact:</span><span>{{
                                        item.rejected?.contactNumber || 'N/A' }}</span></div>
                                <div class="detail-row"><span class="detail-label">Email:</span><span>{{
                                        item.rejected?.email || 'N/A' }}</span></div>
                            </div>
                            <span *ngIf="!item.rejected" class="text-muted d-block small">N/A</span>
                            <div *ngIf="item.rejectionReason" class="mt-2 small text-danger fst-italic text-start">
                                <strong>Reason:</strong> {{ item.rejectionReason }}
                            </div>

                            <!-- Action Buttons for Super Admin -->
                            <hr *ngIf="currentUserRole === componentRoles.SUPER_ADMIN">
                            <div class="d-flex justify-content-center gap-2 mt-2 flex-wrap"
                                *ngIf="currentUserRole === componentRoles.SUPER_ADMIN">
                                <button type="button" class="btn btn-warning btn-sm flex-fill" style="min-width: 40px;"
                                    title="Edit Report" (click)="openEditModal(item)">
                                    <i class="bi bi-pencil-square"></i>
                                </button>
                                <button type="button" class="btn btn-success btn-sm flex-fill" style="min-width: 40px;"
                                    title="Move to Approved" (click)="openConfirmationModal(item, 'approve')"
                                    [disabled]="acceptingReportId === item.id || rejectingReportId === item.id">
                                    <span *ngIf="acceptingReportId !== item.id"><i class="bi bi-check-circle"></i></span>
                                    <span *ngIf="acceptingReportId === item.id" class="spinner-border spinner-border-sm"
                                        role="status" aria-hidden="true"></span>
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm flex-fill" style="min-width: 40px;"
                                    title="Move to Pending" (click)="openConfirmationModal(item, 'pending')"
                                    [disabled]="acceptingReportId === item.id || rejectingReportId === item.id">
                                    <span *ngIf="acceptingReportId !== item.id"><i class="bi bi-arrow-counterclockwise"></i></span>
                                    <span *ngIf="acceptingReportId === item.id" class="spinner-border spinner-border-sm"
                                        role="status" aria-hidden="true"></span>
                                </button>
                            </div>
                        </td>
                        <td class="text-center align-middle"> <!-- Rejected Date -->
                            <span *ngIf="item.rejectedTimestamp" style="color: #dc3545; font-size: 0.9em;">
                                {{ item.rejectedTimestamp | date:'dd MMM yyyy' }} <br>
                                <small>{{ item.rejectedTimestamp | date:'h:mm a' }}</small>
                            </span>
                            <span *ngIf="!item.rejectedTimestamp" style="color: #dc3545; font-size: 0.9em;">
                                {{ item.updatedTimestamp | date:'dd MMM yyyy' }} <br>
                                <small>{{ item.updatedTimestamp | date:'h:mm a' }}</small>
                            </span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- ======================= -->
<!-- Offcanvas Components    -->
<!-- ======================= -->

<!-- Filter Offcanvas (Unchanged) -->
<app-offcanvas [title]="'Filter DigiSafe Reports'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
            <div class="row g-3">
                <!-- Date Filters -->
                <div class="col-md-6"> <label for="filterFromDate" class="form-label small">From Date</label> <input
                        type="date" id="filterFromDate" class="form-control form-control-sm"
                        [(ngModel)]="filters.fromDate" name="fromDateFilter"> </div>
                <div class="col-md-6"> <label for="filterToDate" class="form-label small">To Date</label> <input
                        type="date" id="filterToDate" class="form-control form-control-sm" [(ngModel)]="filters.toDate"
                        name="toDateFilter"> </div>
                <!-- Dropdown Filters -->
                <div class="col-md-6" *ngIf="currentUserRole !== componentRoles.PLANT_ADMIN"> <label for="filterCluster"
                        class="form-label small">Select Cluster</label> <select id="filterCluster"
                        class="form-select form-select-sm" [(ngModel)]="filters.clusterId" name="clusterFilter"
                        (change)="onClusterChange($any(filters.clusterId ?? null))">
                        <option [ngValue]="null">All Clusters</option>
                        <option *ngFor="let cluster of availableClusters" [value]="cluster.id">{{ cluster.title }}
                        </option>
                    </select> </div>
                <div class="col-md-6"> <label for="filterCompany" class="form-label small">Select Company</label>
                    <select id="filterCompany" class="form-select form-select-sm" [(ngModel)]="filters.opcoId"
                        name="companyFilter">
                        <option [ngValue]="null">All Companies</option>
                        <option *ngFor="let company of availableCompanies" [value]="company.id">{{ company.title }}
                        </option>
                    </select> </div>
                <!-- Plant Multi-Select -->
                <div class="col-12">
                    <label for="filterPlants" class="form-label small">Select Plant(s)</label>
                    <ng-select
                        [items]="availablePlants"
                        bindValue="id"
                        bindLabel="name"
                        [multiple]="true"
                        [closeOnSelect]="false"
                        [clearable]="false"
                        [(ngModel)]="filters.plantIds"
                        (change)="updateSelectAllState()"
                        name="plantFilter"
                        placeholder="Select Plant(s)">
                        <ng-template ng-header-tmp>
                            <div class="form-check mb-1 ms-2">
                                <input class="form-check-input" type="checkbox"
                                       id="selectAllPlantsCheckbox"
                                       [checked]="isAllPlantsSelected"
                                       (change)="toggleSelectAllPlants($event)">
                                <label class="form-check-label small" for="selectAllPlantsCheckbox">
                                    Select All / Deselect All
                                </label>
                            </div>
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                            {{ item.name }}
                        </ng-template>
                    </ng-select>
                </div>
                <!-- More Dropdown Filters -->
                <div class="col-md-6"> <label for="filterPlantType" class="form-label small">Select Plant Type</label>
                    <select id="filterPlantType" class="form-select form-select-sm" [(ngModel)]="filters.plantTypeId"
                        name="plantTypeFilter">
                        <option [ngValue]="null">All Plant Types</option>
                        <option *ngFor="let type of availablePlantTypes" [value]="type.id">{{ type.title }}</option>
                    </select> </div>
                <div class="col-md-6"> <label for="filterEnabled" class="form-label small">Enabled Status</label>
                    <select id="filterEnabled" class="form-select form-select-sm" [(ngModel)]="filters.enabled"
                        name="enabledFilter">
                        <option [ngValue]="null">Any Status</option>
                        <option [ngValue]="true">Yes</option>
                        <option [ngValue]="false">No</option>
                    </select> </div>
                <!-- Sort Filter -->
                <div class="col-12"> <label for="filterSort" class="form-label small">Sort By</label> <select
                        id="filterSort" class="form-select form-select-sm" [(ngModel)]="filters.sort" name="sortFilter">
                        <option value="updatedTimestamp,DESC">Newest Submission</option>
                        <option value="updatedTimestamp,ASC">Oldest Submission</option>
                        <option value="plant.name,ASC">Plant Name (A-Z)</option>
                        <option value="plant.name,DESC">Plant Name (Z-A)</option>
                        <option value="id,DESC">Latest ID</option>
                        <option value="id,ASC">Oldest ID</option>
                    </select> </div>
                <!-- Action Buttons -->
                <div class="col-12 mt-4 d-grid gap-2"> <button type="submit" class="btn adani-btn btn-sm"> <i
                            class="bi bi-search me-1"></i> Apply Filters </button> <button type="button"
                        class="btn btn-secondary btn-sm" (click)="resetFilters()"> <i
                            class="bi bi-arrow-clockwise me-1"></i> Reset Filters </button> </div>
            </div>
        </form>
    </div>
</app-offcanvas>

<!-- ============================================ -->
<!-- UPDATED Create DigiSafe Offcanvas -->
<!-- ============================================ -->
<app-offcanvas [title]="'Add New DigiSafe Report'" [width]="'750'" *ngIf="isCreateModalOpen"
    (onClickCross)="closeCreateModal()">
    <div class="create-container p-3" style="max-height: 85vh; overflow-y: auto;">
        <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
            <div class="row g-3">

                <!-- MODIFIED: Plant, Year, Month Selection -->
                <div class="col-12 border-bottom pb-3 mb-3 d-flex flex-wrap gap-3 align-items-end">
                    <!-- Plant Selection -->
                    <div class="flex-grow-1">
                        <label class="form-label" for="createPlantId">Select Plant <span
                                class="text-danger">*</span></label>
                        <select id="createPlantId" class="form-select form-select-sm"
                            [(ngModel)]="newDigisafeData.plantId" name="plantId" required>
                            <option [ngValue]="null" disabled>-- Select Plant --</option>
                            <option *ngFor="let plant of availablePlantsForCreate" [value]="plant.id">{{ plant.name }}
                            </option>
                        </select>
                        <div *ngIf="createForm.controls['plantId']?.invalid && (createForm.controls['plantId']?.dirty || createForm.controls['plantId']?.touched || createForm.submitted)"
                            class="text-danger small mt-1"> Plant is required. </div>
                    </div>
                    <!-- ADDED: Year Selection -->
                    <div style="min-width: 120px;" *ngIf="currentUserRole !== componentRoles.PLANT_ADMIN">
                        <label class="form-label" for="createYear">Year <span class="text-danger">*</span></label>
                        <select id="createYear" class="form-select form-select-sm"
                            [(ngModel)]="newDigisafeData.selectedYear" name="selectedYear" required
                            (ngModelChange)="onYearSelectForCreate()">
                            <option [ngValue]="null" disabled>-- Year --</option>
                            <!-- Use uniqueYearsForCreate generated in TS -->
                            <option *ngFor="let year of uniqueYearsForCreate" [value]="year">{{ year }}</option>
                        </select>
                        <div *ngIf="createForm.controls['selectedYear']?.invalid && (createForm.controls['selectedYear']?.dirty || createForm.controls['selectedYear']?.touched || createForm.submitted)"
                            class="text-danger small mt-1"> Year is required. </div>
                    </div>
                    <!-- ADDED: Month Selection -->
                    <div style="min-width: 120px;">
                        <label class="form-label" for="createMonth">Month <span class="text-danger">*</span></label>
                        <select id="createMonth" class="form-select form-select-sm"
                            [(ngModel)]="newDigisafeData.selectedMonth" name="selectedMonth" required
                            [disabled]="!newDigisafeData.selectedYear"> <!-- Disable if year not selected -->
                            <option [ngValue]="null" disabled>-- Month --</option>
                            <!-- Use filtered months based on selected year -->
                            <ng-container *ngFor="let option of filteredMonthsForCreate">
                                <option [value]="option.month">{{ option.label | slice:0:3 }}</option>
                            </ng-container>
                        </select>
                        <div *ngIf="createForm.controls['selectedMonth']?.invalid && (createForm.controls['selectedMonth']?.dirty || createForm.controls['selectedMonth']?.touched || createForm.submitted)"
                            class="text-danger small mt-1"> Month is required. </div>
                    </div>
                </div>

                <!-- Section 1: Core Statistics -->
                <h6 class="col-12 text-primary mt-2">Core Statistics</h6>
                <div class="col-md-4">
                    <label class="form-label" for="AfpCurrent">AFP (Current) <span class="text-danger">*</span></label>
                    <input type="text" id="AfpCurrent" class="form-control form-control-sm" placeholder="Current AFP"
                        [(ngModel)]="newDigisafeData.AfpCurrent" name="AfpCurrent" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['AfpCurrent']?.invalid && (createForm.controls['AfpCurrent']?.dirty || createForm.controls['AfpCurrent']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['AfpCurrent']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['AfpCurrent']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="AfpBest">AFP (Best) <span class="text-danger">*</span></label>
                    <input type="text" id="AfpBest" class="form-control form-control-sm" placeholder="Best AFP"
                        [(ngModel)]="newDigisafeData.AfpBest" name="AfpBest" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['AfpBest']?.invalid && (createForm.controls['AfpBest']?.dirty || createForm.controls['AfpBest']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['AfpBest']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['AfpBest']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- MODIFIED: Man Hours Worked (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label" for="ManHoursWorked">Man Hours Worked <span
                        class="text-danger">*</span></label>
                    <input type="number" id="ManHoursWorked" class="form-control form-control-sm bg-light"
                        placeholder="Auto-calculated" [(ngModel)]="newDigisafeData.ManHoursWorked"
                        name="ManHoursWorked" readonly>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="KmCovered">Kilometres Covered <span
                            class="text-danger">*</span></label>
                    <input type="text" id="KmCovered" class="form-control form-control-sm" placeholder="KM Covered"
                        [(ngModel)]="newDigisafeData.KmCovered" name="KmCovered" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['KmCovered']?.invalid && (createForm.controls['KmCovered']?.dirty || createForm.controls['KmCovered']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['KmCovered']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['KmCovered']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- Man Hours BreakDown -->
                <div class="col-md-4">
                    <label class="form-label small" for="createManHoursWorkedOwnEmployees">Man Hours (Own) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createManHoursWorkedOwnEmployees" class="form-control form-control-sm"
                        placeholder="Own Emp. Hours" [(ngModel)]="newDigisafeData.ManHoursWorkedOwnEmployees"
                        name="ManHoursWorkedOwnEmployees" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['ManHoursWorkedOwnEmployees']?.invalid && (createForm.controls['ManHoursWorkedOwnEmployees']?.dirty || createForm.controls['ManHoursWorkedOwnEmployees']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['ManHoursWorkedOwnEmployees']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['ManHoursWorkedOwnEmployees']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createManHoursWorkedConEmployees">Man Hours (Con)<span
                        class="text-danger">*</span></label>
                    <input type="text" id="createManHoursWorkedConEmployees" class="form-control form-control-sm"
                        placeholder="Contractor Hours" [(ngModel)]="newDigisafeData.ManHoursWorkedConEmployees"
                        name="ManHoursWorkedConEmployees" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['ManHoursWorkedConEmployees']?.invalid && (createForm.controls['ManHoursWorkedConEmployees']?.dirty || createForm.controls['ManHoursWorkedConEmployees']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['ManHoursWorkedConEmployees']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['ManHoursWorkedConEmployees']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <!-- Section 2: Incidents -->
                <h6 class="col-12 text-primary mt-3 border-top pt-3">Incidents</h6>
                <div class="col-md-4">
                    <label class="form-label" for="OI">Occupational Illness (OI) <span
                            class="text-danger">*</span></label>
                    <input type="text" id="OI" class="form-control form-control-sm" placeholder="OI Count"
                        [(ngModel)]="newDigisafeData.OI" name="OI" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['OI']?.invalid && (createForm.controls['OI']?.dirty || createForm.controls['OI']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['OI']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['OI']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- MODIFIED: FAC (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label" for="FAC">First Aid Cases (FAC) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="FAC" class="form-control form-control-sm bg-light"
                        placeholder="Auto-calculated" [(ngModel)]="newDigisafeData.FAC" name="FAC" readonly>
                </div>
                <!-- FAC Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="createFACCompOnsite">FAC (Emp Onsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createFACCompOnsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.FACCompOnsite" name="FACCompOnsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['FACCompOnsite']?.invalid && (createForm.controls['FACCompOnsite']?.dirty || createForm.controls['FACCompOnsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['FACCompOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['FACCompOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createFACCompOfsite">FAC (Emp Offsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createFACCompOfsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.FACCompOfsite" name="FACCompOfsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['FACCompOfsite']?.invalid && (createForm.controls['FACCompOfsite']?.dirty || createForm.controls['FACCompOfsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['FACCompOfsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['FACCompOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createFACconOnsite">FAC (Con Onsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createFACconOnsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.FACconOnsite" name="FACconOnsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['FACconOnsite']?.invalid && (createForm.controls['FACconOnsite']?.dirty || createForm.controls['FACconOnsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['FACconOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['FACconOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createFACconOfsite">FAC (Con Offsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createFACconOfsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.FACconOfsite" name="FACconOfsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['FACconOfsite']?.invalid && (createForm.controls['FACconOfsite']?.dirty || createForm.controls['FACconOfsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['FACconOfsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['FACconOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <!-- MODIFIED: MTC (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label" for="MTC">Med Treatment Case (MTC) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="MTC" class="form-control form-control-sm bg-light"
                        placeholder="Auto-calculated" [(ngModel)]="newDigisafeData.MTC" name="MTC" readonly>
                </div>
                <!-- MTC Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="createMTCCompOnsite">MTC (Emp Onsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createMTCCompOnsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.MTCCompOnsite" name="MTCCompOnsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['MTCCompOnsite']?.invalid && (createForm.controls['MTCCompOnsite']?.dirty || createForm.controls['MTCCompOnsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['MTCCompOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createMTCCompOfsite">MTC (Emp Offsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createMTCCompOfsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.MTCCompOfsite" name="MTCCompOfsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['MTCCompOfsite']?.invalid && (createForm.controls['MTCCompOfsite']?.dirty || createForm.controls['MTCCompOfsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['MTCCompOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createMTCconOnsite">MTC (Con Onsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createMTCconOnsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.MTCconOnsite" name="MTCconOnsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['MTCconOnsite']?.invalid && (createForm.controls['MTCconOnsite']?.dirty || createForm.controls['MTCconOnsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['MTCconOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createMTCconOfsite">MTC (Con Offsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createMTCconOfsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.MTCconOfsite" name="MTCconOfsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['MTCconOfsite']?.invalid && (createForm.controls['MTCconOfsite']?.dirty || createForm.controls['MTCconOfsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['MTCconOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createMTCthirdPartyVisitor">MTC (Visitor) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createMTCthirdPartyVisitor" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.MTCthirdPartyVisitor"
                        name="MTCthirdPartyVisitor" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['MTCthirdPartyVisitor']?.invalid && (createForm.controls['MTCthirdPartyVisitor']?.dirty || createForm.controls['MTCthirdPartyVisitor']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['MTCthirdPartyVisitor']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- MODIFIED: R-LTI (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label" for="RLTI">R-LTI <span
                        class="text-danger">*</span></label>
                    <input type="text" id="RLTI" class="form-control form-control-sm bg-light"
                        placeholder="Auto-calculated" [(ngModel)]="newDigisafeData.RLTI" name="RLTI" readonly>
                </div>
                <!-- RLTI Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="createRLTICompOnsite">R-LTI (Emp Onsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createRLTICompOnsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.RLTICompOnsite" name="RLTICompOnsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['RLTICompOnsite']?.invalid && (createForm.controls['RLTICompOnsite']?.dirty || createForm.controls['RLTICompOnsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['RLTICompOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createRLTICompOfsite">R-LTI (Emp Offsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createRLTICompOfsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.RLTICompOfsite" name="RLTICompOfsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['RLTICompOfsite']?.invalid && (createForm.controls['RLTICompOfsite']?.dirty || createForm.controls['RLTICompOfsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['RLTICompOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createRLTIconOnsite">R-LTI (Con Onsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createRLTIconOnsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.RLTIconOnsite" name="RLTIconOnsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['RLTIconOnsite']?.invalid && (createForm.controls['RLTIconOnsite']?.dirty || createForm.controls['RLTIconOnsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['RLTIconOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createRLTIconOfsite">R-LTI (Con Offsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createRLTIconOfsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.RLTIconOfsite" name="RLTIconOfsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['RLTIconOfsite']?.invalid && (createForm.controls['RLTIconOfsite']?.dirty || createForm.controls['RLTIconOfsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['RLTIconOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createRLTIthirdPartyVisitor">R-LTI (Visitor) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createRLTIthirdPartyVisitor" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.RLTIthirdPartyVisitor"
                        name="RLTIthirdPartyVisitor" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['RLTIthirdPartyVisitor']?.invalid && (createForm.controls['RLTIthirdPartyVisitor']?.dirty || createForm.controls['RLTIthirdPartyVisitor']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['RLTIthirdPartyVisitor']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- MODIFIED: Fatal (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label" for="Fatal">Fatal <span
                        class="text-danger">*</span></label>
                    <input type="text" id="Fatal" class="form-control form-control-sm bg-light"
                        placeholder="Auto-calculated" [(ngModel)]="newDigisafeData.Fatal" name="Fatal" readonly>
                </div>
                <!-- Fatal Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="createFatalCompOnsite">Fatal (Emp Onsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createFatalCompOnsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.FatalCompOnsite" name="FatalCompOnsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['FatalCompOnsite']?.invalid && (createForm.controls['FatalCompOnsite']?.dirty || createForm.controls['FatalCompOnsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['FatalCompOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createFatalCompOfsite">Fatal (Emp Offsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createFatalCompOfsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.FatalCompOfsite" name="FatalCompOfsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['FatalCompOfsite']?.invalid && (createForm.controls['FatalCompOfsite']?.dirty || createForm.controls['FatalCompOfsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['FatalCompOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createFatalconOnsite">Fatal (Con Onsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createFatalconOnsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.FatalconOnsite" name="FatalconOnsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['FatalconOnsite']?.invalid && (createForm.controls['FatalconOnsite']?.dirty || createForm.controls['FatalconOnsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['FatalconOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createFatalconOfsite">Fatal (Con Offsite) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createFatalconOfsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.FatalconOfsite" name="FatalconOfsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['FatalconOfsite']?.invalid && (createForm.controls['FatalconOfsite']?.dirty || createForm.controls['FatalconOfsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['FatalconOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createFatalthirdPartyVisitor">Fatal (Visitor) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createFatalthirdPartyVisitor" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.FatalthirdPartyVisitor"
                        name="FatalthirdPartyVisitor" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['FatalthirdPartyVisitor']?.invalid && (createForm.controls['FatalthirdPartyVisitor']?.dirty || createForm.controls['FatalthirdPartyVisitor']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['FatalthirdPartyVisitor']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- Other Incident Fields -->
                <div class="col-md-4"> <label class="form-label" for="DangerOccurence">Dangerous Occurrence (DO) <span
                            class="text-danger">*</span></label> <input type="text" id="DangerOccurence"
                        class="form-control form-control-sm" placeholder="DO Count"
                        [(ngModel)]="newDigisafeData.DangerOccurence" name="DangerOccurence" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['DangerOccurence']?.invalid && (createForm.controls['DangerOccurence']?.dirty || createForm.controls['DangerOccurence']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['DangerOccurence']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['DangerOccurence']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label small" for="createPropertyDamage">Property
                        Damage <span
                        class="text-danger">*</span></label> <input type="text" id="createPropertyDamage"
                        class="form-control form-control-sm bg-light" placeholder="Calculated Total"
                        [(ngModel)]="newDigisafeData.propertyDamage" name="propertyDamage" readonly>
                    <small class="text-muted">Sum of Fire and Non-Fire Incidents</small>
                </div>
                <div class="col-md-4"> <label class="form-label" for="FireIncident">Fire Incident <span
                            class="text-danger">*</span></label> <input type="text" id="FireIncident"
                        class="form-control form-control-sm" placeholder="Fire Count"
                        [(ngModel)]="newDigisafeData.FireIncident" name="FireIncident" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (input)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['FireIncident']?.invalid && (createForm.controls['FireIncident']?.dirty || createForm.controls['FireIncident']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['FireIncident']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['FireIncident']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label small" for="createNonFireIncident">Non-Fire
                        Incident <span
                        class="text-danger">*</span></label> <input type="text" id="createNonFireIncident"
                        class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="newDigisafeData.nonFireIncident" name="nonFireIncident" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (input)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['nonFireIncident']?.invalid && (createForm.controls['nonFireIncident']?.dirty || createForm.controls['nonFireIncident']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['nonFireIncident']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['nonFireIncident']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div> </div>
                <div class="col-md-4"> <label class="form-label" for="VRA">Vehicle Related Accident (VRA) <span
                            class="text-danger">*</span></label> <input type="text" id="VRA"
                        class="form-control form-control-sm" placeholder="VRA Count" [(ngModel)]="newDigisafeData.VRA"
                        name="VRA" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['VRA']?.invalid && (createForm.controls['VRA']?.dirty || createForm.controls['VRA']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['VRA']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['VRA']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <div class="col-md-4"> <label class="form-label" for="LSI">Leak-Spill Incident (LSI) <span
                            class="text-danger">*</span></label> <input type="text" id="LSI"
                        class="form-control form-control-sm" placeholder="LSI Count" [(ngModel)]="newDigisafeData.LSI"
                        name="LSI" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['LSI']?.invalid && (createForm.controls['LSI']?.dirty || createForm.controls['LSI']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['LSI']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['LSI']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="NearMissIncident">Near Miss Incident <span
                            class="text-danger">*</span></label> <input type="text" id="NearMissIncident"
                        class="form-control form-control-sm" placeholder="Near Miss Count"
                        [(ngModel)]="newDigisafeData.NearMissIncident" name="NearMissIncident" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['NearMissIncident']?.invalid && (createForm.controls['NearMissIncident']?.dirty || createForm.controls['NearMissIncident']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['NearMissIncident']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['NearMissIncident']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <!-- Section 3: Costs & Losses -->
                <h6 class="col-12 text-primary mt-3 border-top pt-3">Costs & Losses</h6>
                <div class="col-md-4">
                    <label class="form-label" for="AccidentCost">Accident Cost (INR) <span
                            class="text-danger">*</span></label>
                    <input type="text" id="AccidentCost" class="form-control form-control-sm"
                        placeholder="Accident Cost" [(ngModel)]="newDigisafeData.AccidentCost" name="AccidentCost"
                        required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['AccidentCost']?.invalid && (createForm.controls['AccidentCost']?.dirty || createForm.controls['AccidentCost']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['AccidentCost']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['AccidentCost']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- MODIFIED: MDL (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label" for="MDL">Man Days Lost (MDL) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="MDL" class="form-control form-control-sm bg-light"
                        placeholder="Calculated Total" [(ngModel)]="newDigisafeData.MDL" name="MDL" readonly>
                </div>
                <!-- MDL Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="createMDLEmployees">MDL (Employees) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createMDLEmployees" class="form-control form-control-sm" placeholder="Days"
                        [(ngModel)]="newDigisafeData.MDLEmployees" name="MDLEmployees" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['MDLEmployees']?.invalid && (createForm.controls['MDLEmployees']?.dirty || createForm.controls['MDLEmployees']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['MDLEmployees']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['MDLEmployees']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createMDLCon">MDL (Contractor) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createMDLCon" class="form-control form-control-sm" placeholder="Days"
                        [(ngModel)]="newDigisafeData.MDLCon" name="MDLCon" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['MDLCon']?.invalid && (createForm.controls['MDLCon']?.dirty || createForm.controls['MDLCon']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['MDLCon']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['MDLCon']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <!-- Section 4: Safety Activities & Training -->
                <h6 class="col-12 text-primary mt-3 border-top pt-3">Safety Activities & Training</h6>
                <div class="col-md-4"> <label class="form-label" for="SafetyInduction">Safety Induction (Participants)
                        <span class="text-danger">*</span></label> <input type="text" id="SafetyInduction"
                        class="form-control form-control-sm" placeholder="Participants"
                        [(ngModel)]="newDigisafeData.SafetyInduction" name="SafetyInduction" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['SafetyInduction']?.invalid && (createForm.controls['SafetyInduction']?.dirty || createForm.controls['SafetyInduction']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['SafetyInduction']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['SafetyInduction']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="TBTBatches">Tool Box Talk (Batches) <span
                            class="text-danger">*</span></label> <input type="text" id="TBTBatches"
                        class="form-control form-control-sm" placeholder="No. of Batches"
                        [(ngModel)]="newDigisafeData.TBTBatches" name="TBTBatches" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['TBTBatches']?.invalid && (createForm.controls['TBTBatches']?.dirty || createForm.controls['TBTBatches']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['TBTBatches']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['TBTBatches']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="TBTno">TBT Participants <span
                            class="text-danger">*</span></label> <input type="text" id="TBTno"
                        class="form-control form-control-sm" placeholder="Total Participants"
                        [(ngModel)]="newDigisafeData.TBTno" name="TBTno" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['TBTno']?.invalid && (createForm.controls['TBTno']?.dirty || createForm.controls['TBTno']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['TBTno']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['TBTno']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="STP_Plan">STP Plan (Batches) <span
                            class="text-danger">*</span></label> <input type="text" id="STP_Plan"
                        class="form-control form-control-sm" placeholder="Planned Batches"
                        [(ngModel)]="newDigisafeData.STP_Plan" name="STP_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['STP_Plan']?.invalid && (createForm.controls['STP_Plan']?.dirty || createForm.controls['STP_Plan']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['STP_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['STP_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="STP_MTD">STP MTD (Batches) <span
                            class="text-danger">*</span></label> <input type="text" id="STP_MTD"
                        class="form-control form-control-sm" placeholder="MTD Batches"
                        [(ngModel)]="newDigisafeData.STP_MTD" name="STP_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['STP_MTD']?.invalid && (createForm.controls['STP_MTD']?.dirty || createForm.controls['STP_MTD']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['STP_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['STP_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="ET_Plan">Employee Trained Plan (Nos) <span
                            class="text-danger">*</span></label> <input type="text" id="ET_Plan"
                        class="form-control form-control-sm" placeholder="Planned Nos"
                        [(ngModel)]="newDigisafeData.ET_Plan" name="ET_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['ET_Plan']?.invalid && (createForm.controls['ET_Plan']?.dirty || createForm.controls['ET_Plan']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['ET_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['ET_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="ET_MTD">Employee Trained MTD (Nos) <span
                            class="text-danger">*</span></label> <input type="text" id="ET_MTD"
                        class="form-control form-control-sm" placeholder="MTD Nos" [(ngModel)]="newDigisafeData.ET_MTD"
                        name="ET_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['ET_MTD']?.invalid && (createForm.controls['ET_MTD']?.dirty || createForm.controls['ET_MTD']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['ET_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['ET_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- MODIFIED: Training Man Hours (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label" for="TrainingManHours">Training Man-Hours (MTD) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="TrainingManHours" class="form-control form-control-sm bg-light"
                        placeholder="Calculated Total" [(ngModel)]="newDigisafeData.TrainingManHours"
                        name="TrainingManHours" readonly>
                </div>
                <!-- Training Hours Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="createTrainingManHoursEmployees">Training Hours (Emp) <span
                        class="text-danger">*</span></label>
                    <input type="text" id="createTrainingManHoursEmployees" class="form-control form-control-sm"
                        placeholder="Hours" [(ngModel)]="newDigisafeData.TrainingManHoursEmployees"
                        name="TrainingManHoursEmployees" required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['TrainingManHoursEmployees']?.invalid && (createForm.controls['TrainingManHoursEmployees']?.dirty || createForm.controls['TrainingManHoursEmployees']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['TrainingManHoursEmployees']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['TrainingManHoursEmployees']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createTrainingManHoursCon">Training Hours (Con)<span
                        class="text-danger">*</span></label>
                    <input type="text" id="createTrainingManHoursCon" class="form-control form-control-sm"
                        placeholder="Hours" [(ngModel)]="newDigisafeData.TrainingManHoursCon" name="TrainingManHoursCon"
                        required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['TrainingManHoursCon']?.invalid && (createForm.controls['TrainingManHoursCon']?.dirty || createForm.controls['TrainingManHoursCon']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['TrainingManHoursCon']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['TrainingManHoursCon']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- Rest of Safety Activities -->
                <div class="col-md-4"> <label class="form-label" for="SCMPlan">Safety Committee Mtg (Plan)<span
                            class="text-danger">*</span></label> <input type="text" id="SCMPlan"
                        class="form-control form-control-sm" placeholder="Planned Mtgs"
                        [(ngModel)]="newDigisafeData.SCMPlan" name="SCMPlan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['SCMPlan']?.invalid && (createForm.controls['SCMPlan']?.dirty || createForm.controls['SCMPlan']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['SCMPlan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['SCMPlan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="SCM_MTD">Safety Committee Mtg (MTD)<span
                            class="text-danger">*</span></label> <input type="text" id="SCM_MTD"
                        class="form-control form-control-sm" placeholder="MTD Mtgs"
                        [(ngModel)]="newDigisafeData.SCM_MTD" name="SCM_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['SCM_MTD']?.invalid && (createForm.controls['SCM_MTD']?.dirty || createForm.controls['SCM_MTD']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['SCM_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['SCM_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="PWAPlan">PTW Audit (Plan)<span
                            class="text-danger">*</span></label> <input type="text" id="PWAPlan"
                        class="form-control form-control-sm" placeholder="Planned Audits"
                        [(ngModel)]="newDigisafeData.PWAPlan" name="PWAPlan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['PWAPlan']?.invalid && (createForm.controls['PWAPlan']?.dirty || createForm.controls['PWAPlan']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['PWAPlan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['PWAPlan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="PWA_MTD">PTW Audit (MTD)<span
                            class="text-danger">*</span></label> <input type="text" id="PWA_MTD"
                        class="form-control form-control-sm" placeholder="MTD Audits"
                        [(ngModel)]="newDigisafeData.PWA_MTD" name="PWA_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['PWA_MTD']?.invalid && (createForm.controls['PWA_MTD']?.dirty || createForm.controls['PWA_MTD']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['PWA_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['PWA_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="SA_Plan">Self Assessment (Plan)<span
                            class="text-danger">*</span></label> <input type="text" id="SA_Plan"
                        class="form-control form-control-sm" placeholder="Planned Assess."
                        [(ngModel)]="newDigisafeData.SA_Plan" name="SA_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['SA_Plan']?.invalid && (createForm.controls['SA_Plan']?.dirty || createForm.controls['SA_Plan']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['SA_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['SA_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="SA_MTD">Self Assessment (MTD)<span
                            class="text-danger">*</span></label> <input type="text" id="SA_MTD"
                        class="form-control form-control-sm" placeholder="MTD Assess."
                        [(ngModel)]="newDigisafeData.SA_MTD" name="SA_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['SA_MTD']?.invalid && (createForm.controls['SA_MTD']?.dirty || createForm.controls['SA_MTD']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['SA_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['SA_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="SI_Plan">Safety Inspection (Plan)<span
                            class="text-danger">*</span></label> <input type="text" id="SI_Plan"
                        class="form-control form-control-sm" placeholder="Planned Insp."
                        [(ngModel)]="newDigisafeData.SI_Plan" name="SI_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['SI_Plan']?.invalid && (createForm.controls['SI_Plan']?.dirty || createForm.controls['SI_Plan']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['SI_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['SI_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="SI_MTD">Safety Inspection (MTD)<span
                            class="text-danger">*</span></label> <input type="text" id="SI_MTD"
                        class="form-control form-control-sm" placeholder="MTD Insp."
                        [(ngModel)]="newDigisafeData.SI_MTD" name="SI_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['SI_MTD']?.invalid && (createForm.controls['SI_MTD']?.dirty || createForm.controls['SI_MTD']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['SI_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['SI_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="EI_Plan">Equip. Inspection (Plan)<span
                            class="text-danger">*</span></label> <input type="text" id="EI_Plan"
                        class="form-control form-control-sm" placeholder="Planned Insp."
                        [(ngModel)]="newDigisafeData.EI_Plan" name="EI_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['EI_Plan']?.invalid && (createForm.controls['EI_Plan']?.dirty || createForm.controls['EI_Plan']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['EI_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['EI_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="EI_MTD">Equip. Inspection (MTD)<span
                            class="text-danger">*</span></label> <input type="text" id="EI_MTD"
                        class="form-control form-control-sm" placeholder="MTD Insp."
                        [(ngModel)]="newDigisafeData.EI_MTD" name="EI_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['EI_MTD']?.invalid && (createForm.controls['EI_MTD']?.dirty || createForm.controls['EI_MTD']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['EI_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['EI_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="SPC_Plan">Safety Promo Camp. (Plan)<span
                            class="text-danger">*</span></label> <input type="text" id="SPC_Plan"
                        class="form-control form-control-sm" placeholder="Planned Camps."
                        [(ngModel)]="newDigisafeData.SPC_Plan" name="SPC_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['SPC_Plan']?.invalid && (createForm.controls['SPC_Plan']?.dirty || createForm.controls['SPC_Plan']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['SPC_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['SPC_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="SPC_MTD">Safety Promo Camp. (MTD)<span
                            class="text-danger">*</span></label> <input type="text" id="SPC_MTD"
                        class="form-control form-control-sm" placeholder="MTD Camps."
                        [(ngModel)]="newDigisafeData.SPC_MTD" name="SPC_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['SPC_MTD']?.invalid && (createForm.controls['SPC_MTD']?.dirty || createForm.controls['SPC_MTD']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['SPC_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['SPC_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="EMD_Plan">Emergency Mock Drill (Plan)<span
                            class="text-danger">*</span></label> <input type="text" id="EMD_Plan"
                        class="form-control form-control-sm" placeholder="Planned Drills"
                        [(ngModel)]="newDigisafeData.EMD_Plan" name="EMD_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['EMD_Plan']?.invalid && (createForm.controls['EMD_Plan']?.dirty || createForm.controls['EMD_Plan']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['EMD_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['EMD_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="EMD_MTD">Emergency Mock Drill (MTD)<span
                            class="text-danger">*</span></label> <input type="text" id="EMD_MTD"
                        class="form-control form-control-sm" placeholder="MTD Drills"
                        [(ngModel)]="newDigisafeData.EMD_MTD" name="EMD_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['EMD_MTD']?.invalid && (createForm.controls['EMD_MTD']?.dirty || createForm.controls['EMD_MTD']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['EMD_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['EMD_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="IHM_Plan">IH Monitoring (Plan)<span
                            class="text-danger">*</span></label> <input type="text" id="IHM_Plan"
                        class="form-control form-control-sm" placeholder="Planned Mon."
                        [(ngModel)]="newDigisafeData.IHM_Plan" name="IHM_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['IHM_Plan']?.invalid && (createForm.controls['IHM_Plan']?.dirty || createForm.controls['IHM_Plan']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['IHM_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['IHM_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="IHM_MTD">IH Monitoring (MTD)<span
                            class="text-danger">*</span></label> <input type="text" id="IHM_MTD"
                        class="form-control form-control-sm" placeholder="MTD Mon."
                        [(ngModel)]="newDigisafeData.IHM_MTD" name="IHM_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['IHM_MTD']?.invalid && (createForm.controls['IHM_MTD']?.dirty || createForm.controls['IHM_MTD']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['IHM_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['IHM_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="SafetyWalkthrough">Safety Walkthrough (SLT)<span
                            class="text-danger">*</span></label> <input type="text" id="SafetyWalkthrough"
                        class="form-control form-control-sm" placeholder="No. of Walkthroughs"
                        [(ngModel)]="newDigisafeData.SafetyWalkthrough" name="SafetyWalkthrough" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['SafetyWalkthrough']?.invalid && (createForm.controls['SafetyWalkthrough']?.dirty || createForm.controls['SafetyWalkthrough']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['SafetyWalkthrough']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['SafetyWalkthrough']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <!-- Section 5: Safety Notifications -->
                <h6 class="col-12 text-primary mt-3 border-top pt-3">Safety Notifications</h6>
                <div class="col-md-3"> <label class="form-label" for="SnRaised">SN Raised<span
                            class="text-danger">*</span></label> <input type="text" id="SnRaised"
                        class="form-control form-control-sm" placeholder="Raised" [(ngModel)]="newDigisafeData.SnRaised"
                        name="SnRaised" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['SnRaised']?.invalid && (createForm.controls['SnRaised']?.dirty || createForm.controls['SnRaised']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['SnRaised']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['SnRaised']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-3"> <label class="form-label" for="SnClosed">SN Closed<span
                            class="text-danger">*</span></label> <input type="text" id="SnClosed"
                        class="form-control form-control-sm" placeholder="Closed" [(ngModel)]="newDigisafeData.SnClosed"
                        name="SnClosed" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['SnClosed']?.invalid && (createForm.controls['SnClosed']?.dirty || createForm.controls['SnClosed']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['SnClosed']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['SnClosed']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-3"> <label class="form-label" for="SnOpen">SN Open<span
                            class="text-danger">*</span></label> <input type="text" id="SnOpen"
                        class="form-control form-control-sm" placeholder="Open" [(ngModel)]="newDigisafeData.SnOpen"
                        name="SnOpen" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['SnOpen']?.invalid && (createForm.controls['SnOpen']?.dirty || createForm.controls['SnOpen']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['SnOpen']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['SnOpen']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-3"> <label class="form-label" for="SnOpen90days">SN Open > 90 Days<span
                            class="text-danger">*</span></label> <input type="text" id="SnOpen90days"
                        class="form-control form-control-sm" placeholder="Open > 90d"
                        [(ngModel)]="newDigisafeData.SnOpen90days" name="SnOpen90days" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="createForm.controls['SnOpen90days']?.invalid && (createForm.controls['SnOpen90days']?.dirty || createForm.controls['SnOpen90days']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['SnOpen90days']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['SnOpen90days']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <!-- Section 6: RWC / MWD -->
                <h6 class="col-12 text-primary mt-3 border-top pt-3">Work Cases & Days</h6>
                <!-- MODIFIED: RWC (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label small" for="createRWC">RWC (Total)<span
                        class="text-danger">*</span></label>
                    <input type="text" id="createRWC" class="form-control form-control-sm bg-light"
                        placeholder="Calculated Total" [(ngModel)]="newDigisafeData.RWC" name="RWC" readonly>
                </div>
                <!-- RWC Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="createRWCCompOnsite">RWC (Emp Onsite)<span
                        class="text-danger">*</span></label>
                    <input type="text" id="createRWCCompOnsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.RWCCompOnsite" name="RWCCompOnsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['RWCCompOnsite']?.invalid && (createForm.controls['RWCCompOnsite']?.dirty || createForm.controls['RWCCompOnsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['RWCCompOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['RWCCompOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createRWCCompOfsite">RWC (Emp Offsite)<span
                        class="text-danger">*</span></label>
                    <input type="text" id="createRWCCompOfsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.RWCCompOfsite" name="RWCCompOfsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['RWCCompOfsite']?.invalid && (createForm.controls['RWCCompOfsite']?.dirty || createForm.controls['RWCCompOfsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['RWCCompOfsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['RWCCompOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createRWCconOnsite">RWC (Con Onsite)<span
                        class="text-danger">*</span></label>
                    <input type="text" id="createRWCconOnsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.RWCconOnsite" name="RWCconOnsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['RWCconOnsite']?.invalid && (createForm.controls['RWCconOnsite']?.dirty || createForm.controls['RWCconOnsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['RWCconOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['RWCconOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createRWCconOfsite">RWC (Con Offsite)<span
                        class="text-danger">*</span></label>
                    <input type="text" id="createRWCconOfsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="newDigisafeData.RWCconOfsite" name="RWCconOfsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['RWCconOfsite']?.invalid && (createForm.controls['RWCconOfsite']?.dirty || createForm.controls['RWCconOfsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['RWCconOfsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['RWCconOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"></div> <!-- Spacer -->
                <!-- MODIFIED: MWD (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label small" for="createMWD">MWD (Total)<span
                        class="text-danger">*</span></label>
                    <input type="text" id="createMWD" class="form-control form-control-sm bg-light"
                        placeholder="Calculated Total" [(ngModel)]="newDigisafeData.MWD" name="MWD" readonly>
                </div>
                <!-- MWD Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="createMWDEmployee">MWD (Employee)<span
                        class="text-danger">*</span></label>
                    <input type="text" id="createMWDEmployee" class="form-control form-control-sm" placeholder="Days"
                        [(ngModel)]="newDigisafeData.MWDEmployee" name="MWDEmployee"
                        required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['MWDEmployee']?.invalid && (createForm.controls['MWDEmployee']?.dirty || createForm.controls['MWDEmployee']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['MWDEmployee']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['MWDEmployee']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="createMWDContractorOnsite">MWD (Con Onsite)<span
                        class="text-danger">*</span></label>
                    <input type="text" id="createMWDContractorOnsite" class="form-control form-control-sm"
                        placeholder="Days" [(ngModel)]="newDigisafeData.MWDContractorOnsite" name="MWDContractorOnsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['MWDContractorOnsite']?.invalid && (createForm.controls['MWDContractorOnsite']?.dirty || createForm.controls['MWDContractorOnsite']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="createForm.controls['MWDContractorOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="createForm.controls['MWDContractorOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="col-12 mt-4 d-flex justify-content-end gap-2 border-top pt-3">
                    <button type="button" class="btn btn-secondary btn-sm" (click)="closeCreateModal()"> Cancel
                    </button>
                    <button type="submit" class="btn adani-btn btn-sm"
                        [disabled]="createLoading">
                        <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Add Report</span>
                        <span *ngIf="createLoading"> <span class="spinner-border spinner-border-sm me-1" role="status"
                                aria-hidden="true"></span> Adding... </span>
                    </button>
                </div>
            </div> <!-- End row g-3 -->
        </form>
    </div>
</app-offcanvas>
<!-- ********** END: Create DigiSafe Offcanvas ********** -->


<!-- ============================================ -->
<!-- UPDATED EDIT DigiSafe Offcanvas -->
<!-- ============================================ -->
<app-offcanvas [title]="'Edit DigiSafe Report'" [width]="'750'"
    *ngIf="isEditModalOpen && editingFormData && originalEditReport" (onClickCross)="closeEditModal()">
    <div class="edit-container p-3" style="max-height: 85vh; overflow-y: auto;">
        <form #editForm="ngForm" (ngSubmit)="submitEditForm()">
            <div class="row g-3">

                <!-- MODIFIED: Plant, Year, Month Display (Readonly) -->
                <div class="col-12 border-bottom pb-3 mb-3 d-flex flex-wrap gap-3 align-items-end">
                    <div class="flex-grow-1">
                        <label class="form-label" for="editPlantDisplay">Plant</label>
                        <input type="text" id="editPlantDisplay" class="form-control form-control-sm w-auto bg-light"
                            [value]="originalEditReport.plant?.name || 'N/A'" readonly disabled>
                    </div>
                    <div style="min-width: 120px;">
                        <label class="form-label" for="editYearDisplay">Year</label>
                        <input type="text" id="editYearDisplay" class="form-control form-control-sm bg-light"
                            [value]="editingFormData.selectedYear || 'N/A'" readonly disabled>
                    </div>
                    <div style="min-width: 120px;">
                        <label class="form-label" for="editMonthDisplay">Month</label>
                        <input type="text" id="editMonthDisplay" class="form-control form-control-sm bg-light"
                            [value]="formatDate(originalEditReport.updatedTimestamp, 'MMM') || 'N/A'" readonly disabled>
                    </div>
                </div>

                <!-- Section 1: Core Stats -->
                <h6 class="col-12 text-primary mt-2">Core Statistics</h6>
                <div class="col-md-4">
                    <label class="form-label" for="editAfpCurrent">AFP (Current) <span
                            class="text-danger">*</span></label>
                    <input type="text" id="editAfpCurrent" class="form-control form-control-sm"
                        placeholder="Current AFP" [(ngModel)]="editingFormData.AfpCurrent" name="AfpCurrent" required
                        pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['AfpCurrent']?.invalid && (editForm.controls['AfpCurrent']?.dirty || editForm.controls['AfpCurrent']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['AfpCurrent']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['AfpCurrent']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="editAfpBest">AFP (Best) <span class="text-danger">*</span></label>
                    <input type="text" id="editAfpBest" class="form-control form-control-sm" placeholder="Best AFP"
                        [(ngModel)]="editingFormData.AfpBest" name="AfpBest" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['AfpBest']?.invalid && (editForm.controls['AfpBest']?.dirty || editForm.controls['AfpBest']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['AfpBest']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['AfpBest']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- MODIFIED: Man Hours Worked (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label" for="editManHoursWorked">Man Hours Worked</label>
                    <input type="text" id="editManHoursWorked" class="form-control form-control-sm bg-light"
                        placeholder="Calculated Total" [(ngModel)]="editingFormData.ManHoursWorked"
                        name="ManHoursWorked" readonly>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="editKmCovered">Kilometres Covered <span
                            class="text-danger">*</span></label>
                    <input type="text" id="editKmCovered" class="form-control form-control-sm"
                        placeholder="KM Covered" [(ngModel)]="editingFormData.KmCovered" name="KmCovered" required
                        pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['KmCovered']?.invalid && (editForm.controls['KmCovered']?.dirty || editForm.controls['KmCovered']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['KmCovered']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['KmCovered']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- Man Hours BreakDown -->
                <div class="col-md-4">
                    <label class="form-label small" for="editManHoursWorkedOwnEmployees">Man Hours (Own) <span class="text-danger">*</span></label>
                    <input type="text" id="editManHoursWorkedOwnEmployees" class="form-control form-control-sm"
                        placeholder="Own Emp. Hours" [(ngModel)]="editingFormData.ManHoursWorkedOwnEmployees"
                        name="ManHoursWorkedOwnEmployees" required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['ManHoursWorkedOwnEmployees']?.invalid && (editForm.controls['ManHoursWorkedOwnEmployees']?.dirty || editForm.controls['ManHoursWorkedOwnEmployees']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['ManHoursWorkedOwnEmployees']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['ManHoursWorkedOwnEmployees']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editManHoursWorkedConEmployees">Man Hours (Con) <span class="text-danger">*</span></label>
                    <input type="text" id="editManHoursWorkedConEmployees" class="form-control form-control-sm"
                        placeholder="Contractor Hours" [(ngModel)]="editingFormData.ManHoursWorkedConEmployees"
                        name="ManHoursWorkedConEmployees" required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['ManHoursWorkedConEmployees']?.invalid && (editForm.controls['ManHoursWorkedConEmployees']?.dirty || editForm.controls['ManHoursWorkedConEmployees']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['ManHoursWorkedConEmployees']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['ManHoursWorkedConEmployees']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <!-- Section 2: Incidents -->
                <h6 class="col-12 text-primary mt-3 border-top pt-3">Incidents</h6>
                <div class="col-md-4">
                    <label class="form-label" for="editOI">Occupational Illness (OI) <span
                            class="text-danger">*</span></label>
                    <input type="text" id="editOI" class="form-control form-control-sm" placeholder="OI Count"
                        [(ngModel)]="editingFormData.OI" name="OI" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['OI']?.invalid && (editForm.controls['OI']?.dirty || editForm.controls['OI']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['OI']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['OI']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- MODIFIED: FAC (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label" for="editFAC">First Aid Cases (FAC)</label>
                    <input type="text" id="editFAC" class="form-control form-control-sm bg-light"
                        placeholder="Calculated Total" [(ngModel)]="editingFormData.FAC" name="FAC" readonly>
                </div>
                <!-- FAC Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="editFACCompOnsite">FAC (Emp Onsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editFACCompOnsite" class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.FACCompOnsite" name="FACCompOnsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['FACCompOnsite']?.invalid && (editForm.controls['FACCompOnsite']?.dirty || editForm.controls['FACCompOnsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['FACCompOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['FACCompOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editFACCompOfsite">FAC (Emp Offsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editFACCompOfsite" class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.FACCompOfsite" name="FACCompOfsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['FACCompOfsite']?.invalid && (editForm.controls['FACCompOfsite']?.dirty || editForm.controls['FACCompOfsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['FACCompOfsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['FACCompOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editFACconOnsite">FAC (Con Onsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editFACconOnsite" class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.FACconOnsite" name="FACconOnsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['FACconOnsite']?.invalid && (editForm.controls['FACconOnsite']?.dirty || editForm.controls['FACconOnsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['FACconOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['FACconOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editFACconOfsite">FAC (Con Offsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editFACconOfsite" class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.FACconOfsite" name="FACconOfsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['FACconOfsite']?.invalid && (editForm.controls['FACconOfsite']?.dirty || editForm.controls['FACconOfsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['FACconOfsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['FACconOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <!-- MODIFIED: MTC (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label" for="editMTC">Med Treatment Case (MTC)</label>
                    <input type="text" id="editMTC" class="form-control form-control-sm bg-light"
                        placeholder="Calculated Total" [(ngModel)]="editingFormData.MTC" name="MTC" readonly>
                </div>
                <!-- MTC Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="editMTCCompOnsite">MTC (Emp Onsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editMTCCompOnsite" class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.MTCCompOnsite" name="MTCCompOnsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['MTCCompOnsite']?.invalid && (editForm.controls['MTCCompOnsite']?.dirty || editForm.controls['MTCCompOnsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['MTCCompOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['MTCCompOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editMTCCompOfsite">MTC (Emp Offsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editMTCCompOfsite" class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.MTCCompOfsite" name="MTCCompOfsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['MTCCompOfsite']?.invalid && (editForm.controls['MTCCompOfsite']?.dirty || editForm.controls['MTCCompOfsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['MTCCompOfsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['MTCCompOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editMTCconOnsite">MTC (Con Onsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editMTCconOnsite" class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.MTCconOnsite" name="MTCconOnsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['MTCconOnsite']?.invalid && (editForm.controls['MTCconOnsite']?.dirty || editForm.controls['MTCconOnsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['MTCconOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['MTCconOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editMTCconOfsite">MTC (Con Offsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editMTCconOfsite" class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.MTCconOfsite" name="MTCconOfsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['MTCconOfsite']?.invalid && (editForm.controls['MTCconOfsite']?.dirty || editForm.controls['MTCconOfsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['MTCconOfsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['MTCconOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editMTCthirdPartyVisitor">MTC (Visitor) <span class="text-danger">*</span></label>
                    <input type="text" id="editMTCthirdPartyVisitor" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="editingFormData.MTCthirdPartyVisitor"
                        name="MTCthirdPartyVisitor" required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['MTCthirdPartyVisitor']?.invalid && (editForm.controls['MTCthirdPartyVisitor']?.dirty || editForm.controls['MTCthirdPartyVisitor']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['MTCthirdPartyVisitor']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['MTCthirdPartyVisitor']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- MODIFIED: R-LTI (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label" for="editRLTI">R-LTI</label>
                    <input type="text" id="editRLTI" class="form-control form-control-sm bg-light"
                        placeholder="Calculated Total" [(ngModel)]="editingFormData.RLTI" name="RLTI" readonly>
                </div>
                <!-- RLTI Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="editRLTICompOnsite">R-LTI (Emp Onsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editRLTICompOnsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="editingFormData.RLTICompOnsite" name="RLTICompOnsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['RLTICompOnsite']?.invalid && (editForm.controls['RLTICompOnsite']?.dirty || editForm.controls['RLTICompOnsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['RLTICompOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['RLTICompOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editRLTICompOfsite">R-LTI (Emp Offsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editRLTICompOfsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="editingFormData.RLTICompOfsite" name="RLTICompOfsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['RLTICompOfsite']?.invalid && (editForm.controls['RLTICompOfsite']?.dirty || editForm.controls['RLTICompOfsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['RLTICompOfsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['RLTICompOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editRLTIconOnsite">R-LTI (Con Onsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editRLTIconOnsite" class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.RLTIconOnsite" name="RLTIconOnsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['RLTIconOnsite']?.invalid && (editForm.controls['RLTIconOnsite']?.dirty || editForm.controls['RLTIconOnsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['RLTIconOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['RLTIconOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editRLTIconOfsite">R-LTI (Con Offsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editRLTIconOfsite" class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.RLTIconOfsite" name="RLTIconOfsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['RLTIconOfsite']?.invalid && (editForm.controls['RLTIconOfsite']?.dirty || editForm.controls['RLTIconOfsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['RLTIconOfsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['RLTIconOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editRLTIthirdPartyVisitor">R-LTI (Visitor) <span class="text-danger">*</span></label>
                    <input type="text" id="editRLTIthirdPartyVisitor" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="editingFormData.RLTIthirdPartyVisitor"
                        name="RLTIthirdPartyVisitor" required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['RLTIthirdPartyVisitor']?.invalid && (editForm.controls['RLTIthirdPartyVisitor']?.dirty || editForm.controls['RLTIthirdPartyVisitor']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['RLTIthirdPartyVisitor']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['RLTIthirdPartyVisitor']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- MODIFIED: Fatal (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label" for="editFatal">Fatal</label>
                    <input type="text" id="editFatal" class="form-control form-control-sm bg-light"
                        placeholder="Calculated Total" [(ngModel)]="editingFormData.Fatal" name="Fatal" readonly>
                </div>
                <!-- Fatal Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="editFatalCompOnsite">Fatal (Emp Onsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editFatalCompOnsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="editingFormData.FatalCompOnsite" name="FatalCompOnsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['FatalCompOnsite']?.invalid && (editForm.controls['FatalCompOnsite']?.dirty || editForm.controls['FatalCompOnsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['FatalCompOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['FatalCompOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editFatalCompOfsite">Fatal (Emp Offsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editFatalCompOfsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="editingFormData.FatalCompOfsite" name="FatalCompOfsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['FatalCompOfsite']?.invalid && (editForm.controls['FatalCompOfsite']?.dirty || editForm.controls['FatalCompOfsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['FatalCompOfsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['FatalCompOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editFatalconOnsite">Fatal (Con Onsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editFatalconOnsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="editingFormData.FatalconOnsite" name="FatalconOnsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['FatalconOnsite']?.invalid && (editForm.controls['FatalconOnsite']?.dirty || editForm.controls['FatalconOnsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['FatalconOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['FatalconOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editFatalconOfsite">Fatal (Con Offsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editFatalconOfsite" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="editingFormData.FatalconOfsite" name="FatalconOfsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['FatalconOfsite']?.invalid && (editForm.controls['FatalconOfsite']?.dirty || editForm.controls['FatalconOfsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['FatalconOfsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['FatalconOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editFatalthirdPartyVisitor">Fatal (Visitor) <span class="text-danger">*</span></label>
                    <input type="text" id="editFatalthirdPartyVisitor" class="form-control form-control-sm"
                        placeholder="Count" [(ngModel)]="editingFormData.FatalthirdPartyVisitor"
                        name="FatalthirdPartyVisitor" required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['FatalthirdPartyVisitor']?.invalid && (editForm.controls['FatalthirdPartyVisitor']?.dirty || editForm.controls['FatalthirdPartyVisitor']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['FatalthirdPartyVisitor']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['FatalthirdPartyVisitor']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- Other Incident Fields -->
                <div class="col-md-4"> <label class="form-label" for="editDangerOccurence">Dangerous Occurrence (DO)
                        <span class="text-danger">*</span></label> <input type="text" id="editDangerOccurence"
                        class="form-control form-control-sm" placeholder="DO Count"
                        [(ngModel)]="editingFormData.DangerOccurence" name="DangerOccurence" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['DangerOccurence']?.invalid && (editForm.controls['DangerOccurence']?.dirty || editForm.controls['DangerOccurence']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['DangerOccurence']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['DangerOccurence']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label small" for="editPropertyDamage">Property Damage <span class="text-danger">*</span></label>
                    <input type="text" id="editPropertyDamage" class="form-control form-control-sm bg-light"
                        placeholder="Calculated Total" [(ngModel)]="editingFormData.propertyDamage" name="propertyDamage" readonly>
                    <small class="text-muted">Sum of Fire and Non-Fire Incidents</small>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editFireIncident">Fire Incident <span
                            class="text-danger">*</span></label> <input type="text" id="editFireIncident"
                        class="form-control form-control-sm" placeholder="Fire Count"
                        [(ngModel)]="editingFormData.FireIncident" name="FireIncident" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (input)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['FireIncident']?.invalid && (editForm.controls['FireIncident']?.dirty || editForm.controls['FireIncident']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['FireIncident']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['FireIncident']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label small" for="editNonFireIncident">Non-Fire
                        Incident <span class="text-danger">*</span></label> <input type="text" id="editNonFireIncident"
                        class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.nonFireIncident" name="nonFireIncident" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (input)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['nonFireIncident']?.invalid && (editForm.controls['nonFireIncident']?.dirty || editForm.controls['nonFireIncident']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['nonFireIncident']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['nonFireIncident']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editVRA">Vehicle Related Accident (VRA) <span
                            class="text-danger">*</span></label> <input type="text" id="editVRA"
                        class="form-control form-control-sm" placeholder="VRA Count" [(ngModel)]="editingFormData.VRA"
                        name="VRA" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['VRA']?.invalid && (editForm.controls['VRA']?.dirty || editForm.controls['VRA']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['VRA']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['VRA']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <div class="col-md-4"> <label class="form-label" for="editLSI">Leak-Spill Incident (LSI) <span
                            class="text-danger">*</span></label> <input type="text" id="editLSI"
                        class="form-control form-control-sm" placeholder="LSI Count" [(ngModel)]="editingFormData.LSI"
                        name="LSI" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['LSI']?.invalid && (editForm.controls['LSI']?.dirty || editForm.controls['LSI']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['LSI']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['LSI']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editNearMissIncident">Near Miss Incident <span
                            class="text-danger">*</span></label> <input type="text" id="editNearMissIncident"
                        class="form-control form-control-sm" placeholder="Near Miss Count"
                        [(ngModel)]="editingFormData.NearMissIncident" name="NearMissIncident" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['NearMissIncident']?.invalid && (editForm.controls['NearMissIncident']?.dirty || editForm.controls['NearMissIncident']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['NearMissIncident']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['NearMissIncident']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <!-- Section 3: Costs & Losses -->
                <h6 class="col-12 text-primary mt-3 border-top pt-3">Costs & Losses</h6>
                <div class="col-md-4">
                    <label class="form-label" for="editAccidentCost">Accident Cost (INR) <span
                            class="text-danger">*</span></label>
                    <input type="text" id="editAccidentCost" class="form-control form-control-sm"
                        placeholder="Accident Cost" [(ngModel)]="editingFormData.AccidentCost" name="AccidentCost"
                        required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['AccidentCost']?.invalid && (editForm.controls['AccidentCost']?.dirty || editForm.controls['AccidentCost']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['AccidentCost']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['AccidentCost']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- MODIFIED: MDL (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label" for="editMDL">Man Days Lost (MDL)</label>
                    <input type="text" id="editMDL" class="form-control form-control-sm bg-light"
                        placeholder="Calculated Total" [(ngModel)]="editingFormData.MDL" name="MDL" readonly>
                </div>
                <!-- MDL Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="editMDLEmployees">MDL (Employees) <span class="text-danger">*</span></label>
                    <input type="text" id="editMDLEmployees" class="form-control form-control-sm" placeholder="Days"
                        [(ngModel)]="editingFormData.MDLEmployees" name="MDLEmployees" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['MDLEmployees']?.invalid && (editForm.controls['MDLEmployees']?.dirty || editForm.controls['MDLEmployees']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['MDLEmployees']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['MDLEmployees']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editMDLCon">MDL (Contractor) <span class="text-danger">*</span></label>
                    <input type="text" id="editMDLCon" class="form-control form-control-sm" placeholder="Days"
                        [(ngModel)]="editingFormData.MDLCon" name="MDLCon" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['MDLCon']?.invalid && (editForm.controls['MDLCon']?.dirty || editForm.controls['MDLCon']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['MDLCon']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['MDLCon']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <!-- Section 4: Safety Activities & Training -->
                <h6 class="col-12 text-primary mt-3 border-top pt-3">Safety Activities & Training</h6>
                <div class="col-md-4"> <label class="form-label" for="editSafetyInduction">Safety Induction
                        (Participants) <span class="text-danger">*</span></label> <input type="text"
                        id="editSafetyInduction" class="form-control form-control-sm" placeholder="Participants"
                        [(ngModel)]="editingFormData.SafetyInduction" name="SafetyInduction" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['SafetyInduction']?.invalid && (editForm.controls['SafetyInduction']?.dirty || editForm.controls['SafetyInduction']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['SafetyInduction']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['SafetyInduction']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editTBTBatches">Tool Box Talk (Batches) <span
                            class="text-danger">*</span></label> <input type="text" id="editTBTBatches"
                        class="form-control form-control-sm" placeholder="No. of Batches"
                        [(ngModel)]="editingFormData.TBTBatches" name="TBTBatches" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['TBTBatches']?.invalid && (editForm.controls['TBTBatches']?.dirty || editForm.controls['TBTBatches']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['TBTBatches']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['TBTBatches']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editTBTno">TBT Participants <span
                            class="text-danger">*</span></label> <input type="text" id="editTBTno"
                        class="form-control form-control-sm" placeholder="Total Participants"
                        [(ngModel)]="editingFormData.TBTno" name="TBTno" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['TBTno']?.invalid && (editForm.controls['TBTno']?.dirty || editForm.controls['TBTno']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['TBTno']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['TBTno']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editSTP_Plan">STP Plan (Batches) <span
                            class="text-danger">*</span></label> <input type="text" id="editSTP_Plan"
                        class="form-control form-control-sm" placeholder="Planned Batches"
                        [(ngModel)]="editingFormData.STP_Plan" name="STP_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['STP_Plan']?.invalid && (editForm.controls['STP_Plan']?.dirty || editForm.controls['STP_Plan']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['STP_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['STP_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editSTP_MTD">STP MTD (Batches) <span
                            class="text-danger">*</span></label> <input type="text" id="editSTP_MTD"
                        class="form-control form-control-sm" placeholder="MTD Batches"
                        [(ngModel)]="editingFormData.STP_MTD" name="STP_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['STP_MTD']?.invalid && (editForm.controls['STP_MTD']?.dirty || editForm.controls['STP_MTD']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['STP_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['STP_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editET_Plan">Employee Trained Plan (Nos) <span
                            class="text-danger">*</span></label> <input type="text" id="editET_Plan"
                        class="form-control form-control-sm" placeholder="Planned Nos"
                        [(ngModel)]="editingFormData.ET_Plan" name="ET_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['ET_Plan']?.invalid && (editForm.controls['ET_Plan']?.dirty || editForm.controls['ET_Plan']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['ET_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['ET_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editET_MTD">Employee Trained MTD (Nos) <span
                            class="text-danger">*</span></label> <input type="text" id="editET_MTD"
                        class="form-control form-control-sm" placeholder="MTD Nos" [(ngModel)]="editingFormData.ET_MTD"
                        name="ET_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['ET_MTD']?.invalid && (editForm.controls['ET_MTD']?.dirty || editForm.controls['ET_MTD']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['ET_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['ET_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- MODIFIED: Training Man Hours (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label" for="editTrainingManHours">Training Man-Hours (MTD)</label>
                    <input type="text" id="editTrainingManHours" class="form-control form-control-sm bg-light"
                        placeholder="Calculated Total" [(ngModel)]="editingFormData.TrainingManHours"
                        name="TrainingManHours" readonly>
                </div>
                <!-- Training Hours Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="editTrainingManHoursEmployees">Training Hours (Emp) <span class="text-danger">*</span></label>
                    <input type="text" id="editTrainingManHoursEmployees" class="form-control form-control-sm"
                        placeholder="Hours" [(ngModel)]="editingFormData.TrainingManHoursEmployees"
                        name="TrainingManHoursEmployees" required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['TrainingManHoursEmployees']?.invalid && (editForm.controls['TrainingManHoursEmployees']?.dirty || editForm.controls['TrainingManHoursEmployees']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['TrainingManHoursEmployees']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['TrainingManHoursEmployees']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editTrainingManHoursCon">Training Hours (Con) <span class="text-danger">*</span></label>
                    <input type="text" id="editTrainingManHoursCon" class="form-control form-control-sm"
                        placeholder="Hours" [(ngModel)]="editingFormData.TrainingManHoursCon" name="TrainingManHoursCon"
                        required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['TrainingManHoursCon']?.invalid && (editForm.controls['TrainingManHoursCon']?.dirty || editForm.controls['TrainingManHoursCon']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['TrainingManHoursCon']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['TrainingManHoursCon']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <!-- Rest of Safety Activities -->
                <div class="col-md-4"> <label class="form-label" for="editSCMPlan">Safety Committee Mtg (Plan) <span
                            class="text-danger">*</span></label> <input type="text" id="editSCMPlan"
                        class="form-control form-control-sm" placeholder="Planned Mtgs"
                        [(ngModel)]="editingFormData.SCMPlan" name="SCMPlan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['SCMPlan']?.invalid && (editForm.controls['SCMPlan']?.dirty || editForm.controls['SCMPlan']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['SCMPlan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['SCMPlan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editSCM_MTD">Safety Committee Mtg (MTD) <span
                            class="text-danger">*</span></label> <input type="text" id="editSCM_MTD"
                        class="form-control form-control-sm" placeholder="MTD Mtgs"
                        [(ngModel)]="editingFormData.SCM_MTD" name="SCM_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['SCM_MTD']?.invalid && (editForm.controls['SCM_MTD']?.dirty || editForm.controls['SCM_MTD']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['SCM_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['SCM_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editPWAPlan">PTW Audit (Plan) <span
                            class="text-danger">*</span></label> <input type="text" id="editPWAPlan"
                        class="form-control form-control-sm" placeholder="Planned Audits"
                        [(ngModel)]="editingFormData.PWAPlan" name="PWAPlan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['PWAPlan']?.invalid && (editForm.controls['PWAPlan']?.dirty || editForm.controls['PWAPlan']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['PWAPlan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['PWAPlan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editPWA_MTD">PTW Audit (MTD) <span
                            class="text-danger">*</span></label> <input type="text" id="editPWA_MTD"
                        class="form-control form-control-sm" placeholder="MTD Audits"
                        [(ngModel)]="editingFormData.PWA_MTD" name="PWA_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['PWA_MTD']?.invalid && (editForm.controls['PWA_MTD']?.dirty || editForm.controls['PWA_MTD']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['PWA_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['PWA_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editSA_Plan">Self Assessment (Plan) <span
                            class="text-danger">*</span></label> <input type="text" id="editSA_Plan"
                        class="form-control form-control-sm" placeholder="Planned Assess."
                        [(ngModel)]="editingFormData.SA_Plan" name="SA_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['SA_Plan']?.invalid && (editForm.controls['SA_Plan']?.dirty || editForm.controls['SA_Plan']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['SA_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['SA_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editSA_MTD">Self Assessment (MTD) <span
                            class="text-danger">*</span></label> <input type="text" id="editSA_MTD"
                        class="form-control form-control-sm" placeholder="MTD Assess."
                        [(ngModel)]="editingFormData.SA_MTD" name="SA_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['SA_MTD']?.invalid && (editForm.controls['SA_MTD']?.dirty || editForm.controls['SA_MTD']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['SA_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['SA_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editSI_Plan">Safety Inspection (Plan) <span
                            class="text-danger">*</span></label> <input type="text" id="editSI_Plan"
                        class="form-control form-control-sm" placeholder="Planned Insp."
                        [(ngModel)]="editingFormData.SI_Plan" name="SI_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['SI_Plan']?.invalid && (editForm.controls['SI_Plan']?.dirty || editForm.controls['SI_Plan']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['SI_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['SI_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editSI_MTD">Safety Inspection (MTD) <span
                            class="text-danger">*</span></label> <input type="text" id="editSI_MTD"
                        class="form-control form-control-sm" placeholder="MTD Insp."
                        [(ngModel)]="editingFormData.SI_MTD" name="SI_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['SI_MTD']?.invalid && (editForm.controls['SI_MTD']?.dirty || editForm.controls['SI_MTD']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['SI_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['SI_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editEI_Plan">Equip. Inspection (Plan) <span
                            class="text-danger">*</span></label> <input type="text" id="editEI_Plan"
                        class="form-control form-control-sm" placeholder="Planned Insp."
                        [(ngModel)]="editingFormData.EI_Plan" name="EI_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['EI_Plan']?.invalid && (editForm.controls['EI_Plan']?.dirty || editForm.controls['EI_Plan']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['EI_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['EI_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editEI_MTD">Equip. Inspection (MTD) <span
                            class="text-danger">*</span></label> <input type="text" id="editEI_MTD"
                        class="form-control form-control-sm" placeholder="MTD Insp."
                        [(ngModel)]="editingFormData.EI_MTD" name="EI_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['EI_MTD']?.invalid && (editForm.controls['EI_MTD']?.dirty || editForm.controls['EI_MTD']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['EI_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['EI_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editSPC_Plan">Safety Promo Camp. (Plan) <span
                            class="text-danger">*</span></label> <input type="text" id="editSPC_Plan"
                        class="form-control form-control-sm" placeholder="Planned Camps."
                        [(ngModel)]="editingFormData.SPC_Plan" name="SPC_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['SPC_Plan']?.invalid && (editForm.controls['SPC_Plan']?.dirty || editForm.controls['SPC_Plan']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['SPC_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['SPC_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editSPC_MTD">Safety Promo Camp. (MTD) <span
                            class="text-danger">*</span></label> <input type="text" id="editSPC_MTD"
                        class="form-control form-control-sm" placeholder="MTD Camps."
                        [(ngModel)]="editingFormData.SPC_MTD" name="SPC_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['SPC_MTD']?.invalid && (editForm.controls['SPC_MTD']?.dirty || editForm.controls['SPC_MTD']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['SPC_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['SPC_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editEMD_Plan">Emergency Mock Drill (Plan) <span
                            class="text-danger">*</span></label> <input type="text" id="editEMD_Plan"
                        class="form-control form-control-sm" placeholder="Planned Drills"
                        [(ngModel)]="editingFormData.EMD_Plan" name="EMD_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['EMD_Plan']?.invalid && (editForm.controls['EMD_Plan']?.dirty || editForm.controls['EMD_Plan']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['EMD_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['EMD_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editEMD_MTD">Emergency Mock Drill (MTD) <span
                            class="text-danger">*</span></label> <input type="text" id="editEMD_MTD"
                        class="form-control form-control-sm" placeholder="MTD Drills"
                        [(ngModel)]="editingFormData.EMD_MTD" name="EMD_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['EMD_MTD']?.invalid && (editForm.controls['EMD_MTD']?.dirty || editForm.controls['EMD_MTD']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['EMD_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['EMD_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editIHM_Plan">IH Monitoring (Plan) <span
                            class="text-danger">*</span></label> <input type="text" id="editIHM_Plan"
                        class="form-control form-control-sm" placeholder="Planned Mon."
                        [(ngModel)]="editingFormData.IHM_Plan" name="IHM_Plan" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['IHM_Plan']?.invalid && (editForm.controls['IHM_Plan']?.dirty || editForm.controls['IHM_Plan']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['IHM_Plan']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['IHM_Plan']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editIHM_MTD">IH Monitoring (MTD) <span
                            class="text-danger">*</span></label> <input type="text" id="editIHM_MTD"
                        class="form-control form-control-sm" placeholder="MTD Mon."
                        [(ngModel)]="editingFormData.IHM_MTD" name="IHM_MTD" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['IHM_MTD']?.invalid && (editForm.controls['IHM_MTD']?.dirty || editForm.controls['IHM_MTD']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['IHM_MTD']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['IHM_MTD']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"> <label class="form-label" for="editSafetyWalkthrough">Safety Walkthrough (SLT)
                        <span class="text-danger">*</span></label> <input type="text" id="editSafetyWalkthrough"
                        class="form-control form-control-sm" placeholder="No. of Walkthroughs"
                        [(ngModel)]="editingFormData.SafetyWalkthrough" name="SafetyWalkthrough" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['SafetyWalkthrough']?.invalid && (editForm.controls['SafetyWalkthrough']?.dirty || editForm.controls['SafetyWalkthrough']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['SafetyWalkthrough']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['SafetyWalkthrough']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <!-- Section 5: Safety Notifications -->
                <h6 class="col-12 text-primary mt-3 border-top pt-3">Safety Notifications</h6>
                <div class="col-md-3"> <label class="form-label" for="editSnRaised">SN Raised <span
                            class="text-danger">*</span></label> <input type="text" id="editSnRaised"
                        class="form-control form-control-sm" placeholder="Raised" [(ngModel)]="editingFormData.SnRaised"
                        name="SnRaised" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['SnRaised']?.invalid && (editForm.controls['SnRaised']?.dirty || editForm.controls['SnRaised']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['SnRaised']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['SnRaised']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-3"> <label class="form-label" for="editSnClosed">SN Closed <span
                            class="text-danger">*</span></label> <input type="text" id="editSnClosed"
                        class="form-control form-control-sm" placeholder="Closed" [(ngModel)]="editingFormData.SnClosed"
                        name="SnClosed" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['SnClosed']?.invalid && (editForm.controls['SnClosed']?.dirty || editForm.controls['SnClosed']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['SnClosed']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['SnClosed']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-3"> <label class="form-label" for="editSnOpen">SN Open <span
                            class="text-danger">*</span></label> <input type="text" id="editSnOpen"
                        class="form-control form-control-sm" placeholder="Open" [(ngModel)]="editingFormData.SnOpen"
                        name="SnOpen" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['SnOpen']?.invalid && (editForm.controls['SnOpen']?.dirty || editForm.controls['SnOpen']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['SnOpen']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['SnOpen']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-3"> <label class="form-label" for="editSnOpen90days">SN Open > 90 Days <span
                            class="text-danger">*</span></label> <input type="text" id="editSnOpen90days"
                        class="form-control form-control-sm" placeholder="Open > 90d"
                        [(ngModel)]="editingFormData.SnOpen90days" name="SnOpen90days" required pattern="^[0-9]{1,10}$" maxlength="10">
                    <div *ngIf="editForm.controls['SnOpen90days']?.invalid && (editForm.controls['SnOpen90days']?.dirty || editForm.controls['SnOpen90days']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['SnOpen90days']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['SnOpen90days']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <!-- Section 6: RWC / MWD -->
                <h6 class="col-12 text-primary mt-3 border-top pt-3">Work Cases & Days</h6>
                <!-- MODIFIED: RWC (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label small" for="editRWC">RWC (Total)</label>
                    <input type="text" id="editRWC" class="form-control form-control-sm bg-light"
                        placeholder="Calculated Total" [(ngModel)]="editingFormData.RWC" name="RWC" readonly>
                </div>
                <!-- RWC Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="editRWCCompOnsite">RWC (Emp Onsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editRWCCompOnsite" class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.RWCCompOnsite" name="RWCCompOnsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['RWCCompOnsite']?.invalid && (editForm.controls['RWCCompOnsite']?.dirty || editForm.controls['RWCCompOnsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['RWCCompOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['RWCCompOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editRWCCompOfsite">RWC (Emp Offsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editRWCCompOfsite" class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.RWCCompOfsite" name="RWCCompOfsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['RWCCompOfsite']?.invalid && (editForm.controls['RWCCompOfsite']?.dirty || editForm.controls['RWCCompOfsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['RWCCompOfsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['RWCCompOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editRWCconOnsite">RWC (Con Onsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editRWCconOnsite" class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.RWCconOnsite" name="RWCconOnsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['RWCconOnsite']?.invalid && (editForm.controls['RWCconOnsite']?.dirty || editForm.controls['RWCconOnsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['RWCconOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['RWCconOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editRWCconOfsite">RWC (Con Offsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editRWCconOfsite" class="form-control form-control-sm" placeholder="Count"
                        [(ngModel)]="editingFormData.RWCconOfsite" name="RWCconOfsite" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['RWCconOfsite']?.invalid && (editForm.controls['RWCconOfsite']?.dirty || editForm.controls['RWCconOfsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['RWCconOfsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['RWCconOfsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4"></div> <!-- Spacer -->
                <!-- MODIFIED: MWD (Readonly) -->
                <div class="col-md-4">
                    <label class="form-label small" for="editMWD">MWD (Total)</label>
                    <input type="text" id="editMWD" class="form-control form-control-sm bg-light"
                        placeholder="Calculated Total" [(ngModel)]="editingFormData.MWD" name="MWD" readonly>
                </div>
                <!-- MWD Breakdown -->
                <div class="col-md-4">
                    <label class="form-label small" for="editMWDEmployee">MWD (Employee) <span class="text-danger">*</span></label>
                    <input type="text" id="editMWDEmployee" class="form-control form-control-sm" placeholder="Days"
                        [(ngModel)]="editingFormData.MWDEmployee" name="MWDEmployee" required pattern="^[0-9]{1,10}$" maxlength="10"
                        (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['MWDEmployee']?.invalid && (editForm.controls['MWDEmployee']?.dirty || editForm.controls['MWDEmployee']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['MWDEmployee']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['MWDEmployee']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label small" for="editMWDContractorOnsite">MWD (Con Onsite) <span class="text-danger">*</span></label>
                    <input type="text" id="editMWDContractorOnsite" class="form-control form-control-sm"
                        placeholder="Days" [(ngModel)]="editingFormData.MWDContractorOnsite" name="MWDContractorOnsite"
                        required pattern="^[0-9]{1,10}$" maxlength="10" (ngModelChange)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['MWDContractorOnsite']?.invalid && (editForm.controls['MWDContractorOnsite']?.dirty || editForm.controls['MWDContractorOnsite']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <span *ngIf="editForm.controls['MWDContractorOnsite']?.errors?.['required']">This field is required.</span>
                        <span *ngIf="editForm.controls['MWDContractorOnsite']?.errors?.['pattern']">Please enter a valid number (max 10 digits).</span>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="col-12 mt-4 d-flex justify-content-end gap-2 border-top pt-3">
                    <button type="button" class="btn btn-secondary btn-sm" (click)="closeEditModal()"> Cancel </button>
                    <button type="submit" class="btn adani-btn btn-sm" [disabled]="editForm?.invalid || editLoading">
                        <span *ngIf="!editLoading"><i class="bi bi-save-fill me-1"></i> Save Changes</span>
                        <span *ngIf="editLoading"> <span class="spinner-border spinner-border-sm me-1" role="status"
                                aria-hidden="true"></span> Saving... </span>
                    </button>
                </div>
            </div> <!-- End row g-3 -->
        </form>
    </div>
</app-offcanvas>
<!-- ********** END: Edit DigiSafe Offcanvas ********** -->

<!-- Confirmation Modal (Unchanged) -->
<div class="modal fade" #digisafeActionConfirmationModalElement id="digisafeActionConfirmationModal" tabindex="-1"
    aria-labelledby="digisafeActionConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header text-white" [ngClass]="confirmButtonClass">
                <h5 class="modal-title" id="digisafeActionConfirmationModalLabel"> <i [ngClass]="confirmIconClass"
                        class="me-2"></i> {{ modalTitle }} </h5> <button type="button" class="btn-close btn-close-white"
                    aria-label="Close" (click)="closeConfirmationModal()"></button>
            </div>
            <div class="modal-body">
                <p>{{ modalMessage }}</p>
                <div *ngIf="itemToConfirmAction" class="mt-2 p-2 border rounded bg-light small">
                    <div><strong>Report ID:</strong> {{ itemToConfirmAction.id }}</div>
                    <div><strong>Plant:</strong> {{ itemToConfirmAction.plant?.name }}</div>
                    <div><strong>Submitted:</strong> {{ itemToConfirmAction.updatedTimestamp | date:'medium' }}</div>
                </div>
            </div>
            <div class="modal-footer justify-content-center"> <button type="button" class="btn btn-secondary btn-sm"
                    (click)="closeConfirmationModal()"> <i class="bi bi-x-lg me-1"></i> Cancel </button> <button
                    type="button" class="btn btn-sm" [ngClass]="confirmButtonClass.replace('bg-', 'btn-')"
                    (click)="confirmAction()" [disabled]="acceptingReportId !== null || rejectingReportId !== null">
                    <span *ngIf="acceptingReportId === null && rejectingReportId === null"> <i
                            [ngClass]="confirmIconClass" class="me-1"></i> {{ confirmButtonText }} </span> <span
                        *ngIf="acceptingReportId !== null || rejectingReportId !== null"> <span
                            class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                        Processing... </span> </button> </div>
        </div>
    </div>
</div>